=== 2025-06-23 08:42:20 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
ERROR:    [Errno 98] Address already in use
=== 2025-06-23 12:22:14 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [14129] using WatchFiles
INFO:     Started server process [14132]
INFO:     Waiting for application startup.
2025-06-23 12:22:20.572 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 12:22:20.572 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 12:22:20.573 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 12:22:20.748 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 12:22:20.749 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.019s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.030s]
2025-06-23 12:22:20.802 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 12:22:20.803 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 12:22:20.804 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:52172 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 15:37:13 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [14726] using WatchFiles
INFO:     Started server process [14730]
INFO:     Waiting for application startup.
2025-06-23 15:37:21.155 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 15:37:21.156 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 15:37:21.156 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 15:37:21.373 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 15:37:21.374 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.020s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.031s]
2025-06-23 15:37:21.429 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 15:37:21.430 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 15:37:21.430 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:37676 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 18:21:40 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [14967] using WatchFiles
INFO:     Started server process [14974]
INFO:     Waiting for application startup.
2025-06-23 18:21:47.130 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 18:21:47.132 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 18:21:47.133 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 18:21:47.558 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 18:21:47.560 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.022s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.502s]
2025-06-23 18:21:48.094 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 18:21:48.095 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 18:21:48.096 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:45654 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:01:30.180 | INFO     | main:lifespan:95 - 🛑 Shutting down ELK Stack Service...
2025-06-23 20:01:30.215 | INFO     | main:lifespan:99 - ✅ Elasticsearch client closed
2025-06-23 20:01:30.217 | INFO     | main:lifespan:101 - ✅ ELK Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14974]
INFO:     Stopping reloader process [14967]
=== 2025-06-23 20:04:50 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [72258] using WatchFiles
INFO:     Started server process [72262]
INFO:     Waiting for application startup.
2025-06-23 20:04:57.656 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 20:04:57.657 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 20:04:57.664 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 20:04:57.967 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 20:04:57.970 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.054s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.067s]
2025-06-23 20:04:58.101 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 20:04:58.103 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 20:04:58.104 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:06:15.928 | INFO     | main:lifespan:95 - 🛑 Shutting down ELK Stack Service...
2025-06-23 20:06:15.932 | INFO     | main:lifespan:99 - ✅ Elasticsearch client closed
2025-06-23 20:06:15.936 | INFO     | main:lifespan:101 - ✅ ELK Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [72262]
INFO:     Stopping reloader process [72258]
=== 2025-06-23 20:09:27 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [79229] using WatchFiles
INFO:     Started server process [79236]
INFO:     Waiting for application startup.
2025-06-23 20:09:36.208 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 20:09:36.209 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 20:09:36.209 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 20:09:36.602 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 20:09:36.605 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.075s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.174s]
2025-06-23 20:09:36.863 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 20:09:36.865 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 20:09:36.867 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:17:09.281 | INFO     | main:lifespan:95 - 🛑 Shutting down ELK Stack Service...
2025-06-23 20:17:09.316 | INFO     | main:lifespan:99 - ✅ Elasticsearch client closed
2025-06-23 20:17:09.319 | INFO     | main:lifespan:101 - ✅ ELK Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [79236]
INFO:     Stopping reloader process [79229]
=== 2025-06-23 20:21:59 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [88748] using WatchFiles
INFO:     Started server process [88761]
INFO:     Waiting for application startup.
2025-06-23 20:22:05.226 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 20:22:05.227 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 20:22:05.227 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 20:22:05.435 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 20:22:05.437 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.017s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.019s]
2025-06-23 20:22:05.480 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 20:22:05.481 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 20:22:05.482 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:32810 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 21:02:49 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [12058] using WatchFiles
INFO:     Started server process [12071]
INFO:     Waiting for application startup.
2025-06-23 21:02:53.385 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-23 21:02:53.386 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-23 21:02:53.386 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-23 21:02:53.503 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-23 21:02:53.504 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.008s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.008s]
2025-06-23 21:02:53.523 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-23 21:02:53.523 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-23 21:02:53.524 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:37516 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 06:27:48.831 | INFO     | main:lifespan:95 - 🛑 Shutting down ELK Stack Service...
2025-06-24 06:27:49.006 | INFO     | main:lifespan:99 - ✅ Elasticsearch client closed
2025-06-24 06:27:49.031 | INFO     | main:lifespan:101 - ✅ ELK Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [12071]
INFO:     Stopping reloader process [12058]
=== 2025-06-24 06:29:17 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [54136] using WatchFiles
INFO:     Started server process [54139]
INFO:     Waiting for application startup.
2025-06-24 06:29:28.813 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 06:29:28.814 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 06:29:28.815 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-24 06:29:29.075 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-24 06:29:29.077 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.020s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.018s]
2025-06-24 06:29:29.119 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 06:29:29.120 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 06:29:29.121 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:39320 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 09:05:18 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:39344 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:14:41.076 | INFO     | main:lifespan:95 - 🛑 Shutting down ELK Stack Service...
2025-06-24 09:14:42.335 | INFO     | main:lifespan:99 - ✅ Elasticsearch client closed
2025-06-24 09:14:42.362 | INFO     | main:lifespan:101 - ✅ ELK Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [54139]
INFO:     Stopping reloader process [54136]
=== 2025-06-24 09:16:41 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [125022] using WatchFiles
INFO:     Started server process [125025]
INFO:     Waiting for application startup.
2025-06-24 09:16:45.789 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 09:16:45.790 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 09:16:45.790 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-24 09:16:45.962 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-24 09:16:45.963 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.019s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.029s]
2025-06-24 09:16:46.015 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 09:16:46.016 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 09:16:46.016 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 10:14:42 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [11898] using WatchFiles
INFO:     Started server process [11901]
INFO:     Waiting for application startup.
2025-06-24 10:14:48.918 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 10:14:48.918 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 10:14:48.919 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-24 10:14:49.097 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-24 10:14:49.098 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.013s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.012s]
2025-06-24 10:14:49.127 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 10:14:49.128 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 10:14:49.129 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:48866 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 12:36:13 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [22725] using WatchFiles
INFO:     Started server process [22728]
INFO:     Waiting for application startup.
2025-06-24 12:36:20.933 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 12:36:20.934 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 12:36:20.935 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
2025-06-24 12:36:21.198 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for elk-stack
2025-06-24 12:36:21.201 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for elk-stack, using environment variables
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.039s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.076s]
2025-06-24 12:36:21.322 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 12:36:21.323 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 12:36:21.324 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:44578 - "GET / HTTP/1.1" 200 OK
