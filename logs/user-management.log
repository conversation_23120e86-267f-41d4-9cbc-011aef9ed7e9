=== 2025-06-23 08:42:49 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
=== 2025-06-23 12:22:38 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [14466] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [14469]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/enums.py', 'app/services/business/user_management/models/oauth2_models.py', 'app/services/business/user_management/services/email_service.py', 'app/api/auth_endpoints.py', 'app/services/models/enums.py', 'app/api/oauth2_endpoints.py', 'app/services/schemas/role.py', 'app/services/business/user_management/schemas/role.py', 'app/services/business/user_management/enums.py', 'app/services/business/user_management/models/enums.py', 'app/config/oauth2_config.py', 'app/middleware/security_middleware.py', 'app/services/services/auth_service.py', 'app/services/business/user_management/services/user_service.py', 'app/services/business/user_management/db/init_db.py', 'main.py', 'app/services/schemas/user.py', 'app/services/business/user_management/schemas/user.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [14469]
ERROR:main:❌ Failed to include router: invalid decimal literal (auth_endpoints.py, line 947)
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/main.py", line 222, in <module>
    from app.api import router
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/__init__.py", line 10, in <module>
    from .auth_endpoints import router as auth_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py", line 947
    >>>>>>> 105b631cabd4ecef4c0e5554393c9e667c9eb0aa
              ^
SyntaxError: invalid decimal literal
INFO:     Started server process [14997]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Created enum type permissiontype
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Created permission: roles:*
INFO:app.services.business.user_management.db.init_db:Created permission: *:*
INFO:app.services.business.user_management.db.init_db:Created permission: users:write
INFO:app.services.business.user_management.db.init_db:Created permission: users:*
INFO:app.services.business.user_management.db.init_db:Created permission: users:read
INFO:app.services.business.user_management.db.init_db:Created permission: permissions:read
INFO:app.services.business.user_management.db.init_db:Created permission: profile:read
INFO:app.services.business.user_management.db.init_db:Created permission: roles:read
INFO:app.services.business.user_management.db.init_db:Created permission: profile:write
INFO:app.services.business.user_management.db.init_db:Created role: super_admin (Super Admin)
INFO:app.services.business.user_management.db.init_db:  - Added permission *:* to role super_admin
INFO:app.services.business.user_management.db.init_db:Created role: admin (Admin)
INFO:app.services.business.user_management.db.init_db:  - Added permission users:* to role admin
INFO:app.services.business.user_management.db.init_db:  - Added permission roles:* to role admin
INFO:app.services.business.user_management.db.init_db:  - Added permission permissions:read to role admin
INFO:app.services.business.user_management.db.init_db:Created role: manager (Manager)
INFO:app.services.business.user_management.db.init_db:  - Added permission users:read to role manager
INFO:app.services.business.user_management.db.init_db:  - Added permission users:write to role manager
INFO:app.services.business.user_management.db.init_db:  - Added permission roles:read to role manager
INFO:app.services.business.user_management.db.init_db:Created role: user (User)
INFO:app.services.business.user_management.db.init_db:  - Added permission profile:read to role user
INFO:app.services.business.user_management.db.init_db:  - Added permission profile:write to role user
INFO:app.services.business.user_management.db.init_db:Created role: guest (Guest)
INFO:app.services.business.user_management.db.init_db:  - Added permission profile:read to role guest
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:35672 - "GET / HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [14997]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [15216]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:51828 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:51828 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:57872 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:42016 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.services.business.user_management.services.user_service:Assigned default 'user' <NAME_EMAIL>
INFO:app.services.business.user_management.services.user_service:Created user <NAME_EMAIL>
ERROR:app.services.business.user_management.services.user_service:Error creating user: (psycopg2.errors.NotNullViolation) null value in column "created_at" of relation "user_profiles" violates not-null constraint
DETAIL:  Failing row contains (0ac613d9-ddeb-41e3-88b0-b62550fb00b4, null, null, null, null, null, null, null, null, null, null, en, light, t, t, null, null, null, null, null, null, {}, null, null).

[SQL: INSERT INTO user_profiles (id, user_id, phone_number, company, job_title, address, city, state, country, postal_code, timezone, language, theme, notifications_enabled, email_notifications, website, linkedin_url, twitter_handle, github_username, bio, avatar_url, metadata) VALUES (%(id)s::UUID, %(user_id)s::UUID, %(phone_number)s, %(company)s, %(job_title)s, %(address)s, %(city)s, %(state)s, %(country)s, %(postal_code)s, %(timezone)s, %(language)s, %(theme)s, %(notifications_enabled)s, %(email_notifications)s, %(website)s, %(linkedin_url)s, %(twitter_handle)s, %(github_username)s, %(bio)s, %(avatar_url)s, %(metadata)s)]
[parameters: {'id': UUID('0ac613d9-ddeb-41e3-88b0-b62550fb00b4'), 'user_id': None, 'phone_number': None, 'company': None, 'job_title': None, 'address': None, 'city': None, 'state': None, 'country': None, 'postal_code': None, 'timezone': None, 'language': 'en', 'theme': 'light', 'notifications_enabled': True, 'email_notifications': True, 'website': None, 'linkedin_url': None, 'twitter_handle': None, 'github_username': None, 'bio': None, 'avatar_url': None, 'metadata': '{}'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:app.api.auth_endpoints:Unexpected registration error: Error creating user: (psycopg2.errors.NotNullViolation) null value in column "created_at" of relation "user_profiles" violates not-null constraint
DETAIL:  Failing row contains (0ac613d9-ddeb-41e3-88b0-b62550fb00b4, null, null, null, null, null, null, null, null, null, null, en, light, t, t, null, null, null, null, null, null, {}, null, null).

[SQL: INSERT INTO user_profiles (id, user_id, phone_number, company, job_title, address, city, state, country, postal_code, timezone, language, theme, notifications_enabled, email_notifications, website, linkedin_url, twitter_handle, github_username, bio, avatar_url, metadata) VALUES (%(id)s::UUID, %(user_id)s::UUID, %(phone_number)s, %(company)s, %(job_title)s, %(address)s, %(city)s, %(state)s, %(country)s, %(postal_code)s, %(timezone)s, %(language)s, %(theme)s, %(notifications_enabled)s, %(email_notifications)s, %(website)s, %(linkedin_url)s, %(twitter_handle)s, %(github_username)s, %(bio)s, %(avatar_url)s, %(metadata)s)]
[parameters: {'id': UUID('0ac613d9-ddeb-41e3-88b0-b62550fb00b4'), 'user_id': None, 'phone_number': None, 'company': None, 'job_title': None, 'address': None, 'city': None, 'state': None, 'country': None, 'postal_code': None, 'timezone': None, 'language': 'en', 'theme': 'light', 'notifications_enabled': True, 'email_notifications': True, 'website': None, 'linkedin_url': None, 'twitter_handle': None, 'github_username': None, 'bio': None, 'avatar_url': None, 'metadata': '{}'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
INFO:     127.0.0.1:53388 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [15216]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [20689]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [20689]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [20841]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [20841]
INFO:     Stopping reloader process [14466]
=== 2025-06-23 12:41:47 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [21571] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [21574]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.services.business.user_management.services.user_service:Assigned default 'user' <NAME_EMAIL>
ERROR:app.services.business.user_management.services.user_service:Error creating user: Error creating UserProfile: 'created_at' is an invalid keyword argument for UserProfile
ERROR:app.api.auth_endpoints:Unexpected registration error: Error creating user: Error creating UserProfile: 'created_at' is an invalid keyword argument for UserProfile
INFO:     127.0.0.1:32972 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/db/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [21574]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [22972]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [22972]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [23159]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [23159]
INFO:     Stopping reloader process [21571]
=== 2025-06-23 12:44:37 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [23816] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [23820]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/db/init_db.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [23820]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [24762]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [24762]
INFO:     Stopping reloader process [23816]
=== 2025-06-23 12:46:14 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [25244] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [25249]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.services.business.user_management.services.user_service:Assigned default 'user' <NAME_EMAIL>
INFO:app.services.business.user_management.services.user_service:Created user <NAME_EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:35872 - "POST /api/v1/auth/register HTTP/1.1" 201 Created
INFO:     127.0.0.1:35358 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:35362 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38398 - "GET /api/v1/auth/oauth/callback/github?code=3c443f3ac5b36028b3ab HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:38402 - "GET /favicon.ico HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'app/services/services/oauth2_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [25249]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [33356]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [33356]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [44422]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [44422]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [45298]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45298]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [45685]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45685]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [45918]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:48298 - "GET /api/v1/auth/oauth/callback/github?code=dc7d81dc3ba591c86e2e HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:48314 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60784 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47780 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:47780 - "GET /openapi.json HTTP/1.1" 200 OK
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:41942 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/services/oauth2_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45918]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [68862]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/db/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [68862]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [72275]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
ERROR:app.services.business.user_management.db.init_db:Error creating default roles and permissions: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('e9b3e498-07af-4a84-ac66-9cedfe573e32')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
psycopg2.errors.UndefinedColumn: column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/services/business/user_management/db/init_db.py", line 192, in create_default_roles
    permission = db.query(Permission).filter(Permission.name == perm_id).first()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2748, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1803, in first
    return self._only_one_row(
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 757, in _only_one_row
    row: Optional[_InterimRowType[Any]] = onerow(hard_close=True)
                                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1690, in _fetchone_impl
    return self._real_result._fetchone_impl(hard_close=hard_close)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 2282, in _fetchone_impl
    row = next(self.iterator, _NO_ROW)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 242, in chunks
    post_load.invoke(context, path)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 1539, in invoke
    loader(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3206, in _load_for_path
    self._load_via_parent(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3282, in _load_via_parent
    context.session.execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
           ^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('e9b3e498-07af-4a84-ac66-9cedfe573e32')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:app.services.business.user_management.db.init_db:Error initializing database: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('e9b3e498-07af-4a84-ac66-9cedfe573e32')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:main:❌ Database initialization failed: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('e9b3e498-07af-4a84-ac66-9cedfe573e32')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
WARNING:main:⚠️ Service will continue without database initialization
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/oauth2_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72275]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [73105]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
ERROR:app.services.business.user_management.db.init_db:Error creating default roles and permissions: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
psycopg2.errors.UndefinedColumn: column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/services/business/user_management/db/init_db.py", line 192, in create_default_roles
    permission = db.query(Permission).filter(Permission.name == perm_id).first()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2748, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1803, in first
    return self._only_one_row(
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 757, in _only_one_row
    row: Optional[_InterimRowType[Any]] = onerow(hard_close=True)
                                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1690, in _fetchone_impl
    return self._real_result._fetchone_impl(hard_close=hard_close)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 2282, in _fetchone_impl
    row = next(self.iterator, _NO_ROW)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 242, in chunks
    post_load.invoke(context, path)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 1539, in invoke
    loader(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3206, in _load_for_path
    self._load_via_parent(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3282, in _load_via_parent
    context.session.execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
           ^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:app.services.business.user_management.db.init_db:Error initializing database: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:main:❌ Database initialization failed: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
WARNING:main:⚠️ Service will continue without database initialization
INFO:     Application startup complete.
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:35530 - "GET /api/v1/auth/oauth/callback/github?code=dc7d81dc3ba591c86e2e HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:41272 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53498 - "GET /api/v1/auth/oauth/callback/github HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:53504 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57894 - "GET /api/v1/auth/oauth/login/github?code=3cf0a6b861d213481fa3 HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57902 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:43610 - "GET /api/v1/auth/oauth/callback/github?code=eb0c556f6a66ca4905b8 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:43618 - "GET /favicon.ico HTTP/1.1" 404 Not Found
=== 2025-06-23 15:37:41 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [15127] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [15132]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
ERROR:app.services.business.user_management.db.init_db:Error creating default roles and permissions: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
psycopg2.errors.UndefinedColumn: column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/services/business/user_management/db/init_db.py", line 192, in create_default_roles
    permission = db.query(Permission).filter(Permission.name == perm_id).first()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2748, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1803, in first
    return self._only_one_row(
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 757, in _only_one_row
    row: Optional[_InterimRowType[Any]] = onerow(hard_close=True)
                                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1690, in _fetchone_impl
    return self._real_result._fetchone_impl(hard_close=hard_close)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 2282, in _fetchone_impl
    row = next(self.iterator, _NO_ROW)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 242, in chunks
    post_load.invoke(context, path)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 1539, in invoke
    loader(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3206, in _load_for_path
    self._load_via_parent(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3282, in _load_via_parent
    context.session.execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
           ^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:app.services.business.user_management.db.init_db:Error initializing database: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:main:❌ Database initialization failed: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
WARNING:main:⚠️ Service will continue without database initialization
INFO:     Application startup complete.
INFO:     127.0.0.1:44188 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:57620 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:57628 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57638 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:57638 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:40686 - "GET /api/v1/auth/oauth/login/github/?github_client_id=Iv23liionxalH49IaWp6&redirect_uri=http://localhost:7301/&scope=user:email&response_type=code HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57436 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:57450 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40672 - "GET /api/oauth2/github/authorize?redirect_uri=http://localhost:7301/api/v1/auth/oauth/callback/github HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40680 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45436 - "GET /api/V1/auth/oauth/login/github/authorize?redirect_uri=http://localhost:7301/api/v1/auth/oauth/callback/github HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45440 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41808 - "GET /api/v1/auth/oauth/login/github/authorize?redirect_uri=http://localhost:7301/api/v1/auth/oauth/callback/github HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59690 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:47116 - "GET /api/v1/auth/oauth/callback/github?code=a24e2d9cb5941a16a048 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:34262 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:55458 - "GET /api/v1/auth/oauth/callback/github?code=828856a0b3e9aae74b1d HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:55464 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:49574 - "GET /api/v1/auth/oauth/callback/github?code=1e82f8eca6eefe0fa2d0 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:49584 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:38466 - "GET /api/v1/auth/oauth/callback/github?code=649cb020644b2d7708d5 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:38474 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [15132]
INFO:     Stopping reloader process [15127]
=== 2025-06-23 16:09:49 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [44028] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [44031]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
ERROR:app.services.business.user_management.db.init_db:Error creating default roles and permissions: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
psycopg2.errors.UndefinedColumn: column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/services/business/user_management/db/init_db.py", line 192, in create_default_roles
    permission = db.query(Permission).filter(Permission.name == perm_id).first()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2748, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1803, in first
    return self._only_one_row(
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 757, in _only_one_row
    row: Optional[_InterimRowType[Any]] = onerow(hard_close=True)
                                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 1690, in _fetchone_impl
    return self._real_result._fetchone_impl(hard_close=hard_close)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py", line 2282, in _fetchone_impl
    row = next(self.iterator, _NO_ROW)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 242, in chunks
    post_load.invoke(context, path)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/loading.py", line 1539, in invoke
    loader(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3206, in _load_for_path
    self._load_via_parent(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py", line 3282, in _load_via_parent
    context.session.execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
           ^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:app.services.business.user_management.db.init_db:Error initializing database: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
ERROR:main:❌ Database initialization failed: (psycopg2.errors.UndefinedColumn) column users.oauth_access_token does not exist
LINE 1: ...users.oauth_account_id AS users_oauth_account_id, users.oaut...
                                                             ^

[SQL: SELECT roles_1.id AS roles_1_id, users.id AS users_id, users.email AS users_email, users.username AS users_username, users.hashed_password AS users_hashed_password, users.first_name AS users_first_name, users.last_name AS users_last_name, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.status AS users_status, users.last_login AS users_last_login, users.failed_login_attempts AS users_failed_login_attempts, users.reset_password_token AS users_reset_password_token, users.reset_password_sent_at AS users_reset_password_sent_at, users.email_verification_token AS users_email_verification_token, users.email_verification_sent_at AS users_email_verification_sent_at, users.oauth_provider AS users_oauth_provider, users.oauth_account_id AS users_oauth_account_id, users.oauth_access_token AS users_oauth_access_token, users.oauth_account_data AS users_oauth_account_data, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM roles AS roles_1 JOIN user_roles AS user_roles_1 ON roles_1.id = user_roles_1.role_id JOIN users ON users.id = user_roles_1.user_id 
WHERE roles_1.id IN (%(primary_keys_1)s::UUID)]
[parameters: {'primary_keys_1': UUID('********-cac3-46e8-8149-46bab1667ab6')}]
(Background on this error at: https://sqlalche.me/e/20/f405)
WARNING:main:⚠️ Service will continue without database initialization
INFO:     Application startup complete.
=== 2025-06-23 16:11:13 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [44031]
INFO:     Stopping reloader process [44028]
=== 2025-06-23 16:13:04 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [45299] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [45302]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:42648 - "GET /api/v1/auth/oauth/callback/github?code=702bb2b074a8a1e10c40 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:42654 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:33552 - "GET /api/v1/auth/oauth/callback/github?code=9f6de6a3e47943707dbc HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:33564 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:46632 - "GET /api/v1/auth/oauth/callback/github?code=649cb020644b2d7708d5 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:46646 - "GET /favicon.ico HTTP/1.1" 404 Not Found
ERROR:app.api.auth_endpoints:OAuth2 callback error: name 'OAuth2Service' is not defined
INFO:     127.0.0.1:48824 - "GET /api/v1/auth/oauth/callback/github?code=c4d69f9de33aafca0044 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:48836 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49136 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:49150 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:38024 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:39518 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
ERROR:app.api.auth_endpoints:Admin list users error: type object 'UserService' has no attribute 'get_users_paginated'
INFO:     127.0.0.1:37524 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Admin list users error: type object 'UserService' has no attribute 'get_users_paginated'
INFO:     127.0.0.1:42652 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Admin list users error: type object 'UserService' has no attribute 'get_users_paginated'
INFO:     127.0.0.1:37508 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Admin list users error: type object 'UserService' has no attribute 'get_users_paginated'
INFO:     127.0.0.1:42414 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45302]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [53182]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [53182]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [53254]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100)
INFO:     127.0.0.1:37216 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:51870 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:38242 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:47760 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:34092 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 400 Bad Request
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.services.business.user_management.services.user_service:Created user <NAME_EMAIL>
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:40526 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 201 Created
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100)
INFO:     127.0.0.1:60698 - "GET /api/v1/auth/admin/users/?email=<EMAIL> HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:39364 - "GET /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [53254]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [55906]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55906]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [55973]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55973]
INFO:     Stopping reloader process [45299]
=== 2025-06-23 16:56:01 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [56606] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [56610]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=<EMAIL>, username=None)
INFO:     127.0.0.1:32986 - "GET /api/v1/auth/admin/users/?email=<EMAIL> HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=None, username=kimm11)
INFO:     127.0.0.1:56672 - "GET /api/v1/auth/admin/users/?username=kimm11 HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=None, username=kim11)
INFO:     127.0.0.1:57936 - "GET /api/v1/auth/admin/users/?username=kim11 HTTP/1.1" 200 OK
INFO:     127.0.0.1:37264 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 405 Method Not Allowed
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [56610]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [57603]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57603]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [57732]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57732]
INFO:     Stopping reloader process [56606]
=== 2025-06-23 17:00:16 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [57895] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [57898]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:42168 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 405 Method Not Allowed
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py', 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57898]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [58027]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58027]
INFO:     Stopping reloader process [57895]
=== 2025-06-23 17:01:00 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [58154] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [58169]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:58850 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 405 Method Not Allowed
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58169]
INFO:     Stopping reloader process [58154]
=== 2025-06-23 17:03:15 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [58791] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [58794]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:50598 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:40942 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:37944 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:51426 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/deactivate HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:52850 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/deactivate HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:40760 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:40364 - "DELETE /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 401 Unauthorized
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:53752 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
ERROR:app.api.auth_endpoints:Admin delete user error: type object 'UserService' has no attribute 'delete_user'
INFO:     127.0.0.1:43012 - "DELETE /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Admin activate user error: type object 'UserService' has no attribute 'activate_user'
INFO:     127.0.0.1:59896 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Admin deactivate user error: type object 'UserService' has no attribute 'deactivate_user'
INFO:     127.0.0.1:51842 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/deactivate HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58794]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [61424]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [61424]
INFO:     Stopping reloader process [58791]
=== 2025-06-23 17:11:13 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [61749] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [61759]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:35114 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [61759]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [62252]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:56530 - "POST /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e/activate HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47314 - "POST /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd/activate HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39660 - "GET /api/v1/auth/admin/users/?email=<EMAIL> HTTP/1.1" 401 Unauthorized
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:46954 - "GET /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 200 OK
INFO:     127.0.0.1:56232 - "POST /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd/activate HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [62252]
INFO:     Stopping reloader process [61749]
=== 2025-06-23 17:16:01 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [63450] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [63453]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:51004 - "POST /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd/activate HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54278 - "GET /api/v1/auth/admin/users/?email=<EMAIL> HTTP/1.1" 401 Unauthorized
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=<EMAIL>, username=None)
INFO:     127.0.0.1:35498 - "GET /api/v1/auth/admin/users/?email=<EMAIL> HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=<EMAIL>/activate, username=None)
INFO:     127.0.0.1:35686 - "GET /api/v1/auth/admin/users/?email=<EMAIL>/activate HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=<EMAIL>/deactivate, username=None)
INFO:     127.0.0.1:48782 - "GET /api/v1/auth/admin/users/?email=<EMAIL>/deactivate HTTP/1.1" 200 OK
INFO:     127.0.0.1:40296 - "POST /api/v1/auth/admin/users/?email=<EMAIL>/deactivate HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:44130 - "POST /api/v1/auth/admin/users/?email=<EMAIL>/activate HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:55826 - "POST /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd/activate HTTP/1.1" 422 Unprocessable Entity
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
ERROR:app.api.auth_endpoints:Admin delete user error: type object 'datetime.datetime' has no attribute 'timezone'
INFO:     127.0.0.1:34294 - "DELETE /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36052 - "PATCH /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 405 Method Not Allowed
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63453]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [67272]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [67272]
INFO:     Stopping reloader process [63450]
=== 2025-06-23 17:29:27 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [67428] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [67432]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:43372 - "DELETE /api/v1/auth/admin/users/9b3c0ce8-8757-4c55-b5fe-c24a2311ad3e HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51458 - "DELETE /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:34454 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:47056 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.services.business.user_management.services.user_service:Created user <NAME_EMAIL>
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:43270 - "POST /api/v1/auth/admin/users/ HTTP/1.1" 201 Created
INFO:     127.0.0.1:43272 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:52652 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:52654 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60104 - "DELETE /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:40966 - "DELETE /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:59936 - "DELETE /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [67432]
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [69613]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69613]
INFO:     Stopping reloader process [67428]
=== 2025-06-23 17:36:43 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [69785] using WatchFiles
INFO:main:✅ Successfully included router with 22 routes
INFO:     Started server process [69788]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:44298 - "DELETE /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:52002 - "PATCH /api/v1/auth/admin/users/?id=81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:42176 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51122 - "PATCH /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:40968 - "GET /api/v1/auth/admin/users/81393725-f446-4862-9f4b-713f7de2f9bd HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:59382 - "GET /api/v1/auth/admin/users HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:54676 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 401 Unauthorized
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=None, username=None)
INFO:     127.0.0.1:35962 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:35594 - "GET /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f/ HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:48442 - "GET /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f/ HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:46332 - "PUT /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f/ HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:54124 - "PATCH /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f/ HTTP/1.1" 307 Temporary Redirect
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:59170 - "GET /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 200 OK
INFO:     127.0.0.1:38294 - "PATCH /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:51394 - "PATCH /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 405 Method Not Allowed
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69788]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [72758]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:55644 - "PUT /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 422 Unprocessable Entity
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72758]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [73034]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:60580 - "PUT /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:51270 - "PUT /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 422 Unprocessable Entity
ERROR:app.api.auth_endpoints:Admin update user error: type object 'UserService' has no attribute 'update_user'
INFO:     127.0.0.1:50974 - "PUT /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 500 Internal Server Error
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:58944 - "PATCH /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:43568 - "DELETE /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f HTTP/1.1" 200 OK
INFO:     127.0.0.1:43862 - "PATCH /api/v1/auth/admin/users/23c768f0-d0c9-44fb-b630-67547787306f/ HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:52024 - "PATCH /api/v1/auth/admin/users/ HTTP/1.1" 405 Method Not Allowed
INFO:app.api.auth_endpoints:Admin <EMAIL> listed users (skip=0, limit=100, email=None, username=None)
INFO:     127.0.0.1:54950 - "GET /api/v1/auth/admin/users/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59348 - "PATCH /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:51372 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42992 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate/ HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:42476 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52046 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 405 Method Not Allowed
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:52048 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [73034]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [77602]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [77602]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [77709]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [77709]
INFO:     Stopping reloader process [69785]
=== 2025-06-23 18:01:19 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [77902] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [77905]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:56470 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 200 OK
ERROR:app.services.business.user_management.services.user_service:Error deactivating user a8903e4e-41ea-4db9-9ffd-933e6d1247bf: INACTIVE
ERROR:app.api.auth_endpoints:Admin deactivate user error: Error deactivating user: INACTIVE
INFO:     127.0.0.1:56476 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/deactivate HTTP/1.1" 500 Internal Server Error
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:33092 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
ERROR:app.services.business.user_management.services.user_service:Error deactivating user a8903e4e-41ea-4db9-9ffd-933e6d1247bf: INACTIVE
ERROR:app.api.auth_endpoints:Admin deactivate user error: Error deactivating user: INACTIVE
INFO:     127.0.0.1:46046 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/deactivate HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/user_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [77905]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [78800]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
=== 2025-06-23 18:22:05 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [15115] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [15122]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:60508 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:58020 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:58020 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:58676 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/deactivate HTTP/1.1" 401 Unauthorized
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:40414 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
ERROR:app.services.business.user_management.services.user_service:Error deactivating user a8903e4e-41ea-4db9-9ffd-933e6d1247bf: INACTIVE
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/app/services/business/user_management/services/user_service.py", line 342, in deactivate_user
    user.status = UserStatus.INACTIVE
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/enum.py", line 786, in __getattr__
    raise AttributeError(name) from None
AttributeError: INACTIVE
ERROR:app.api.auth_endpoints:Admin deactivate user error: Error deactivating user: INACTIVE
INFO:     127.0.0.1:48136 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/deactivate HTTP/1.1" 500 Internal Server Error
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:53932 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:36740 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/models/enums.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [15122]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [27245]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/models/enums.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [27245]
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [27408]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [27408]
INFO:     Stopping reloader process [15115]
=== 2025-06-23 19:09:40 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [27689] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [27694]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:42790 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
INFO:app.services.business.user_management.services.user_service:Deactivated user: a8903e4e-41ea-4db9-9ffd-933e6d1247bf, is_active=False, status=UserStatus.INACTIVE
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:33594 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/deactivate HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:48670 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:44062 - "POST /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf/activate HTTP/1.1" 200 OK
INFO:app.api.auth_endpoints:Admin <EMAIL> <NAME_EMAIL>
INFO:     127.0.0.1:44070 - "GET /api/v1/auth/admin/users/a8903e4e-41ea-4db9-9ffd-933e6d1247bf HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [27694]
INFO:     Stopping reloader process [27689]
=== 2025-06-23 20:05:16 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [72380] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [72385]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:41510 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72385]
INFO:     Stopping reloader process [72380]
=== 2025-06-23 20:22:33 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [89053] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [89076]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:33680 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 21:03:11 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [12171] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [12174]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:51332 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [12174]
INFO:     Stopping reloader process [12171]
=== 2025-06-24 06:29:46 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [54313] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [54316]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:40282 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 09:05:50 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:54466 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [54316]
INFO:     Stopping reloader process [54313]
=== 2025-06-24 09:17:26 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [125252] using WatchFiles
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [125257]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database...
INFO:app.services.business.user_management.db.init_db:Ensuring database tables exist...
INFO:app.services.business.user_management.db.init_db:Enum type permissiontype already exists
INFO:app.services.business.user_management.db.init_db:Database tables ensured to exist
INFO:app.services.business.user_management.db.init_db:Successfully created all default roles and permissions
INFO:app.services.business.user_management.db.init_db:Users already exist, skipping default admin creation
INFO:app.services.business.user_management.db.init_db:Database initialized successfully
INFO:main:✅ User management database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:47690 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:47690 - "GET /openapi.json HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/db/models.py', 'app/services/business/user_management/services/email_service.py', 'app/services/business/user_management/models/enums.py', 'app/services/services/auth_service.py', 'app/services/business/user_management/schemas/user.py', 'app/services/models/enums.py', 'app/client/vault_http_client.py', 'app/services/services/oauth2_service.py', 'app/services/schemas/user.py', 'app/vault_integration.py', 'app/middleware/security_middleware.py', 'app/services/business/user_management/schemas/role.py', 'main.py', 'app/config/oauth2_config.py', 'app/services/schemas/role.py', 'app/startup/vault_startup.py', 'app/services/business/user_management/models/oauth2_models.py', 'app/services/business/user_management/db/init_db.py', 'app/services/business/user_management/services/user_service.py', 'app/api/oauth2_endpoints.py', 'app/services/enums.py', 'app/core/config.py', 'app/services/business/user_management/enums.py', 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [125257]
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:main:✅ Successfully included router with 21 routes
INFO:     Started server process [129671]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/db/models.py', 'app/services/business/user_management/services/email_service.py', 'app/services/business/user_management/models/enums.py', 'app/services/inter_service_client.py', 'app/services/services/auth_service.py', 'app/services/business/user_management/schemas/user.py', 'app/client/vault_http_client.py', 'app/services/models/enums.py', 'app/services/services/oauth2_service.py', 'app/services/postgresql_client.py', 'app/vault_integration.py', 'app/services/schemas/user.py', 'app/services/cluster_db_init.py', 'app/middleware/security_middleware.py', 'app/services/business/user_management/schemas/role.py', 'main.py', 'app/config/oauth2_config.py', 'app/services/schemas/role.py', 'app/startup/vault_startup.py', 'app/services/business/user_management/models/oauth2_models.py', 'app/services/business/user_management/db/init_db.py', 'app/services/business/user_management/services/user_service.py', 'app/api/oauth2_endpoints.py', 'app/services/enums.py', 'app/services/business/user_management/db/base.py', 'app/core/config.py', 'app/services/business/user_management/enums.py', 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [129671]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [129864]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"column \"permissions\" of relation \"roles\" does not exist"}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 09:26:26.623 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 09:26:26.624 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [129864]
INFO:     Stopping reloader process [125252]
=== 2025-06-24 09:27:20 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [130290] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [130293]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"column \"permissions\" of relation \"roles\" does not exist"}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 09:27:29.384 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 09:27:29.385 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [130293]
INFO:     Stopping reloader process [130290]
=== 2025-06-24 09:38:02 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [134004] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [134007]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:     127.0.0.1:40432 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:48486 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:45842 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [14605]
INFO:     Stopping reloader process [12038]
=== 2025-06-24 10:23:44 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [15000] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [15003]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"invalid input for query argument $6: ['*:*'] (expected str, got list)"}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:23:48.680 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:23:48.682 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:34508 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:34510 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53532 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:53532 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:49616 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43744 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [15003]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [17250]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:33:43.470 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:33:43.471 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [17250]
INFO:     Stopping reloader process [15000]
=== 2025-06-24 10:34:11 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [17404] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [17408]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:34:16.102 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:34:16.103 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:58920 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:42108 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:38756 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [17408]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [18887]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
ERROR:app.services.cluster_db_init:❌ Error creating default roles: PostgreSQLClusterClient.select_records() got an unexpected keyword argument 'where'
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:39:05.572 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:39:05.573 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [18887]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [19018]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
ERROR:app.services.cluster_db_init:❌ Error creating default roles: PostgreSQLClusterClient.select_records() got an unexpected keyword argument 'where'
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:39:29.157 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:39:29.158 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [19018]
INFO:     Stopping reloader process [17404]
=== 2025-06-24 10:39:40 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [19136] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [19139]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
ERROR:app.services.cluster_db_init:❌ Error creating default roles: PostgreSQLClusterClient.select_records() got an unexpected keyword argument 'where'
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:39:44.042 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:39:44.043 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:54172 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [19139]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [19781]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:41:37.743 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:41:37.745 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [19781]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [19904]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:42:06.915 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:42:06.917 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [19904]
INFO:     Stopping reloader process [19136]
=== 2025-06-24 10:42:39 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [20123] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [20126]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:42:44.061 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:42:44.062 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:34070 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34070 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34070 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34070 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:45738 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [20126]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [20802]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:45:20.336 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:45:20.338 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [20802]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [20870]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:45:39.481 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:45:39.482 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [20870]
INFO:     Stopping reloader process [20123]
=== 2025-06-24 10:45:51 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [21032] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [21035]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:45:55.726 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:45:55.727 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:50922 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34194 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
=== 2025-06-24 12:36:45 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [22900] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 23 routes
INFO:     Started server process [22903]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
ERROR:app.startup.vault_startup:❌ Failed to connect to vault service: Cannot connect to host localhost:7200 ssl:default [Connection refused]
WARNING:app.startup.vault_startup:⚠️ Vault service not available for user-management, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Initializing user management database via PostgreSQL Cluster Service...
INFO:app.services.cluster_db_init:🚀 Starting database initialization via PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:🏗️ Initializing user management tables...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/tables/create "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ User management tables initialized successfully
INFO:app.services.cluster_db_init:🔧 Creating default roles...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - {"detail":"duplicate key value violates unique constraint \"roles_name_key\"\nDETAIL:  Key (name)=(super_admin) already exists."}
ERROR:app.services.cluster_db_init:❌ Failed to create role: super_admin
ERROR:app.services.cluster_db_init:❌ Failed to create default roles
ERROR:main:❌ Database initialization failed via PostgreSQL Cluster Service
WARNING:main:⚠️ Service will continue without database initialization
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 12:36:54.527 | WARNING  | app.services.inter_service_client:_register_user_management_service:92 - ⚠️ Failed to register with service registry: 401
2025-06-24 12:36:54.528 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:34270 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:56638 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:56648 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43376 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:43384 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34030 - "GET /api/v1/auth/oauth/callback/github?code=7635f1691fdb0ac1faa2 HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34038 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48236 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:48242 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48256 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:48256 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:46444 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:36806 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
