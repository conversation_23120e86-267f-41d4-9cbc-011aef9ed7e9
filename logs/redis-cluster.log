=== 2025-06-23 08:41:21 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
ERROR:    [<PERSON>rrno 98] Address already in use
=== 2025-06-23 12:21:04 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [13303] using WatchFiles
INFO:     Started server process [13309]
INFO:     Waiting for application startup.
2025-06-23 12:21:12.573 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 12:21:12.574 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 12:21:12.575 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 12:21:13.076 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 12:21:13.085 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 12:21:13.243 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 12:21:13.244 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:42448 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 15:36:22 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [14304] using WatchFiles
INFO:     Started server process [14307]
INFO:     Waiting for application startup.
2025-06-23 15:36:26.020 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 15:36:26.021 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 15:36:26.021 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 15:36:26.241 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 15:36:26.243 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 15:36:26.261 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 15:36:26.262 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:50000 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 18:20:45 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [14509] using WatchFiles
INFO:     Started server process [14516]
INFO:     Waiting for application startup.
2025-06-23 18:20:49.484 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 18:20:49.486 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 18:20:49.487 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 18:20:49.789 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 18:20:49.791 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 18:20:49.826 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 18:20:49.827 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:46736 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:01:46.955 | INFO     | main:shutdown_event:593 - 🛑 Shutting down Redis Cluster Service...
2025-06-23 20:01:46.978 | INFO     | main:shutdown_event:597 - ✅ Redis client closed
2025-06-23 20:01:46.979 | INFO     | main:shutdown_event:599 - ✅ Redis Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14516]
INFO:     Stopping reloader process [14509]
=== 2025-06-23 20:03:59 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [71808] using WatchFiles
INFO:     Started server process [71813]
INFO:     Waiting for application startup.
2025-06-23 20:04:03.909 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 20:04:03.910 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 20:04:03.911 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 20:04:04.220 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 20:04:04.222 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 20:04:04.261 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 20:04:04.263 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:06:16.086 | INFO     | main:shutdown_event:593 - 🛑 Shutting down Redis Cluster Service...
2025-06-23 20:06:16.111 | INFO     | main:shutdown_event:597 - ✅ Redis client closed
2025-06-23 20:06:16.118 | INFO     | main:shutdown_event:599 - ✅ Redis Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [71813]
INFO:     Stopping reloader process [71808]
=== 2025-06-23 20:08:22 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [78517] using WatchFiles
INFO:     Started server process [78539]
INFO:     Waiting for application startup.
2025-06-23 20:08:30.157 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 20:08:30.159 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 20:08:30.160 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 20:08:30.787 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 20:08:30.789 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 20:08:31.014 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 20:08:31.029 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:17:09.225 | INFO     | main:shutdown_event:593 - 🛑 Shutting down Redis Cluster Service...
2025-06-23 20:17:09.255 | INFO     | main:shutdown_event:597 - ✅ Redis client closed
2025-06-23 20:17:09.260 | INFO     | main:shutdown_event:599 - ✅ Redis Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [78539]
INFO:     Stopping reloader process [78517]
=== 2025-06-23 20:21:09 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [88424] using WatchFiles
INFO:     Started server process [88430]
INFO:     Waiting for application startup.
2025-06-23 20:21:13.314 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 20:21:13.315 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 20:21:13.316 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 20:21:13.522 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 20:21:13.524 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 20:21:13.547 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 20:21:13.548 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:56464 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 21:02:05 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [11764] using WatchFiles
INFO:     Started server process [11767]
INFO:     Waiting for application startup.
2025-06-23 21:02:08.474 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-23 21:02:08.475 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-23 21:02:08.475 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-23 21:02:08.663 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-23 21:02:08.665 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-23 21:02:08.676 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-23 21:02:08.677 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:46444 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 06:27:49.397 | INFO     | main:shutdown_event:593 - 🛑 Shutting down Redis Cluster Service...
2025-06-24 06:27:49.770 | INFO     | main:shutdown_event:597 - ✅ Redis client closed
2025-06-24 06:27:49.776 | INFO     | main:shutdown_event:599 - ✅ Redis Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [11767]
INFO:     Stopping reloader process [11764]
=== 2025-06-24 06:28:20 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [53654] using WatchFiles
INFO:     Started server process [53668]
INFO:     Waiting for application startup.
2025-06-24 06:28:25.456 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 06:28:25.458 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 06:28:25.458 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-24 06:28:25.829 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-24 06:28:25.832 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-24 06:28:25.861 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-24 06:28:25.862 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:34416 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 09:04:12 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:45314 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:14:40.415 | INFO     | main:shutdown_event:593 - 🛑 Shutting down Redis Cluster Service...
2025-06-24 09:14:40.913 | INFO     | main:shutdown_event:597 - ✅ Redis client closed
2025-06-24 09:14:41.090 | INFO     | main:shutdown_event:599 - ✅ Redis Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [53668]
INFO:     Stopping reloader process [53654]
=== 2025-06-24 09:16:00 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [124773] using WatchFiles
INFO:     Started server process [124779]
INFO:     Waiting for application startup.
2025-06-24 09:16:03.877 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 09:16:03.877 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 09:16:03.878 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-24 09:16:04.015 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-24 09:16:04.016 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-24 09:16:04.026 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-24 09:16:04.027 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 10:13:46 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [11438] using WatchFiles
INFO:     Started server process [11441]
INFO:     Waiting for application startup.
2025-06-24 10:13:51.731 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 10:13:51.733 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 10:13:51.735 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-24 10:13:52.002 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-24 10:13:52.004 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-24 10:13:52.034 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-24 10:13:52.034 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:48094 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 12:35:13 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [22267] using WatchFiles
INFO:     Started server process [22270]
INFO:     Waiting for application startup.
2025-06-24 12:35:18.672 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 12:35:18.674 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 12:35:18.676 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
2025-06-24 12:35:19.093 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for redis-cluster
2025-06-24 12:35:19.097 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for redis-cluster, using environment variables
2025-06-24 12:35:19.154 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-24 12:35:19.155 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36830 - "GET / HTTP/1.1" 200 OK
