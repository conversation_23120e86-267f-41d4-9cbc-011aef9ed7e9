=== 2025-06-23 08:41:10 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
ERROR:    [Errno 98] Address already in use
=== 2025-06-23 12:20:50 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [13107] using WatchFiles
INFO:     Started server process [13145]
INFO:     Waiting for application startup.
2025-06-23 12:20:55.873 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 12:20:55.874 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 12:20:55.874 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 12:20:56.157 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 12:20:56.159 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 12:20:57.306 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 12:21:00.074 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 12:21:00.075 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 12:21:00.172 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 12:21:00.174 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 12:21:00.609 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 12:21:00.611 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 12:21:00.614 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 12:21:01.365 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 12:21:01.370 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 12:21:01.372 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 12:21:01.373 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 12:21:01.374 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 12:21:01.375 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 12:21:01.376 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:43944 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37242 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43622 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60950 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:55122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59062 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34352 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33194 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38434 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43758 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58520 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56808 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50280 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37038 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54872 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57460 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60826 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40772 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60944 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38790 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42312 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46134 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37326 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34782 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55338 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35808 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60344 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55408 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51710 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54422 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33312 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37040 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40400 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55096 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54422 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45698 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47652 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39866 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50644 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41460 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52134 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40822 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41602 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54590 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56502 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34498 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44236 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58826 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37730 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34758 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59062 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55574 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49550 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39096 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49520 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44926 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49818 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34788 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60630 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34152 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35410 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33886 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52220 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45644 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47998 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36548 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37112 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48434 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51218 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57832 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60302 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40406 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34204 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52664 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33990 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35768 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45630 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34060 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51550 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45948 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36130 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33926 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60406 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36372 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47822 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39492 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59352 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43054 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54664 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52158 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55706 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38690 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-23 15:36:11 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [14241] using WatchFiles
INFO:     Started server process [14244]
INFO:     Waiting for application startup.
2025-06-23 15:36:15.435 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 15:36:15.436 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 15:36:15.436 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 15:36:15.668 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 15:36:15.670 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 15:36:16.627 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 15:36:19.213 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 15:36:19.214 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 15:36:19.300 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 15:36:19.301 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 15:36:19.571 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 15:36:19.572 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 15:36:19.574 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 15:36:20.072 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 15:36:20.075 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 15:36:20.076 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 15:36:20.077 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 15:36:20.077 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 15:36:20.078 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 15:36:20.078 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:40562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50038 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:42722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34152 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59710 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41502 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53526 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45842 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46270 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34768 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45620 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38362 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33206 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33230 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34644 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36772 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49528 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44362 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55392 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 16:08:35.672 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-23 16:08:35.690 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-23 16:08:35.692 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14244]
INFO:     Stopping reloader process [14241]
=== 2025-06-23 16:08:46 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [43549] using WatchFiles
INFO:     Started server process [43559]
INFO:     Waiting for application startup.
2025-06-23 16:08:49.493 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 16:08:49.494 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 16:08:49.494 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 16:08:49.630 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 16:08:49.631 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 16:08:50.348 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 16:08:51.586 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 16:08:51.586 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 16:08:51.628 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 16:08:51.629 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 16:08:51.765 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 16:08:51.765 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 16:08:51.766 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 16:08:52.035 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 16:08:52.036 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 16:08:52.036 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 16:08:52.037 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 16:08:52.037 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 16:08:52.037 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 16:08:52.038 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:56546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49118 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 16:12:20.771 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-23 16:12:20.788 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-23 16:12:20.790 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [43559]
INFO:     Stopping reloader process [43549]
=== 2025-06-23 17:16:52 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [63725] using WatchFiles
INFO:     Started server process [63728]
INFO:     Waiting for application startup.
2025-06-23 17:16:54.968 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 17:16:54.969 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 17:16:54.969 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 17:16:55.139 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 17:16:55.141 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 17:16:55.685 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 17:16:56.958 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 17:16:56.959 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 17:16:56.992 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 17:16:56.993 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 17:16:57.127 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 17:16:57.127 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 17:16:57.128 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 17:16:57.373 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 17:16:57.374 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 17:16:57.375 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 17:16:57.375 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 17:16:57.376 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 17:16:57.376 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 17:16:57.376 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36420 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56526 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35788 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55440 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51054 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33352 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52162 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34060 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35230 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45654 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43950 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58372 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37392 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45590 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48998 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49772 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49524 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58804 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59440 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56372 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43456 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46692 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35194 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49616 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51024 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36964 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-23 18:20:32 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [14467] using WatchFiles
INFO:     Started server process [14474]
INFO:     Waiting for application startup.
2025-06-23 18:20:37.308 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 18:20:37.310 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 18:20:37.310 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 18:20:37.544 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 18:20:37.547 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 18:20:38.975 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 18:20:41.328 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 18:20:41.329 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 18:20:41.434 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 18:20:41.436 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 18:20:41.655 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 18:20:41.656 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 18:20:41.658 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 18:20:42.051 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 18:20:42.053 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 18:20:42.054 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 18:20:42.055 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 18:20:42.055 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 18:20:42.056 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 18:20:42.056 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:32890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59856 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35404 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:44108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48498 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54282 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51574 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47100 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33460 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34870 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60852 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58504 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54078 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60456 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40282 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57980 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39452 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36268 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51136 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45504 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47974 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40830 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59892 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34804 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38408 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45994 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41542 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46400 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39112 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50010 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49038 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57818 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60878 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49990 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47404 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50350 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58548 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60660 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47310 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46896 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48832 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43420 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57422 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45654 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54338 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35980 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53010 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35422 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56950 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45474 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56526 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46784 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57842 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:01:44.598 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-23 20:01:44.612 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-23 20:01:44.613 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14474]
INFO:     Stopping reloader process [14467]
=== 2025-06-23 20:03:48 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [71755] using WatchFiles
INFO:     Started server process [71770]
INFO:     Waiting for application startup.
2025-06-23 20:03:54.271 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 20:03:54.272 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 20:03:54.273 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 20:03:54.548 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 20:03:54.550 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 20:03:55.435 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 20:03:57.881 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 20:03:57.881 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 20:03:57.947 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 20:03:57.948 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 20:03:58.210 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 20:03:58.211 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 20:03:58.213 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 20:03:58.712 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:03:58.717 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:03:58.719 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:03:58.721 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:03:58.723 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 20:03:58.724 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 20:03:58.726 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:52306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42548 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60060 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:06:16.007 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-23 20:06:16.309 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-23 20:06:16.328 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [71770]
INFO:     Stopping reloader process [71755]
=== 2025-06-23 20:08:03 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [78277] using WatchFiles
INFO:     Started server process [78346]
INFO:     Waiting for application startup.
2025-06-23 20:08:16.767 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 20:08:16.772 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 20:08:16.773 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 20:08:17.508 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 20:08:17.510 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 20:08:19.366 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 20:08:24.870 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 20:08:24.871 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 20:08:25.003 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 20:08:25.004 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 20:08:25.431 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 20:08:25.433 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 20:08:25.450 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 20:08:26.360 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:08:26.363 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:08:26.364 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:08:26.365 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:08:26.366 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 20:08:26.367 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 20:08:26.368 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50832 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52782 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47948 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40540 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:17:08.972 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-23 20:17:09.050 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-23 20:17:09.056 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [78346]
INFO:     Stopping reloader process [78277]
=== 2025-06-23 20:20:53 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [88330] using WatchFiles
INFO:     Started server process [88334]
INFO:     Waiting for application startup.
2025-06-23 20:21:01.021 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 20:21:01.022 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 20:21:01.023 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 20:21:01.263 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 20:21:01.266 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 20:21:02.198 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 20:21:06.531 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 20:21:06.532 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 20:21:06.651 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 20:21:06.657 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 20:21:07.006 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 20:21:07.008 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 20:21:07.013 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 20:21:07.679 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:21:07.683 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:21:07.684 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:21:07.685 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 20:21:07.686 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 20:21:07.687 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 20:21:07.688 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:33732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42656 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:36202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56990 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48520 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43912 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46652 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39080 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33394 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44052 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36060 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47852 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38804 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39158 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41672 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49448 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34232 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56218 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45614 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52042 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-23 21:01:55 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [11639] using WatchFiles
INFO:     Started server process [11642]
INFO:     Waiting for application startup.
2025-06-23 21:01:59.201 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-23 21:01:59.202 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-23 21:01:59.202 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-23 21:01:59.473 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-23 21:01:59.476 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-23 21:02:00.420 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-23 21:02:02.989 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-23 21:02:02.990 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-23 21:02:03.105 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-23 21:02:03.106 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-23 21:02:03.387 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-23 21:02:03.388 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-23 21:02:03.394 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-23 21:02:03.970 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 21:02:03.973 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 21:02:03.974 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 21:02:03.975 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-23 21:02:03.976 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-23 21:02:03.976 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-23 21:02:03.977 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36082 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39804 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:50026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39312 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58892 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56082 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60052 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58362 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50392 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46474 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34630 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36866 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42550 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46470 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43818 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53382 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52994 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52882 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41996 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47270 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59574 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34658 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37504 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38600 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35622 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59772 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43204 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44882 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55862 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55930 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38406 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48652 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36454 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56406 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49526 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52400 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56218 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43876 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44054 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54448 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34410 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52082 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50214 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46524 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42118 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39140 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38130 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33710 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47528 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33982 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40506 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42456 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43856 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53236 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36080 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33768 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52220 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53096 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42644 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38350 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58282 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51140 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 06:27:48.593 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 06:27:48.616 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 06:27:48.617 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [11642]
INFO:     Stopping reloader process [11639]
=== 2025-06-24 06:28:09 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [53564] using WatchFiles
INFO:     Started server process [53567]
INFO:     Waiting for application startup.
2025-06-24 06:28:13.897 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 06:28:13.898 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 06:28:13.899 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 06:28:14.157 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 06:28:14.160 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 06:28:15.222 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 06:28:17.508 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 06:28:17.510 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 06:28:17.615 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 06:28:17.616 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 06:28:17.877 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 06:28:17.878 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 06:28:17.880 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 06:28:18.449 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 06:28:18.452 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 06:28:18.453 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 06:28:18.454 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 06:28:18.454 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 06:28:18.455 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 06:28:18.456 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:59380 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44744 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45344 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50894 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:50896 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52968 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57620 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56896 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50602 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56602 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40226 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36948 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57326 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40322 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48886 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50400 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39698 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37052 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42654 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51096 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58152 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41606 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53186 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35458 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59374 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42892 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47606 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60344 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57878 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35244 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38622 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36920 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47186 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52152 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34002 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47542 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39930 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54876 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50194 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42606 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34236 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44886 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47254 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49112 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33280 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59002 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42740 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58964 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49190 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60614 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44214 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37600 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38848 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 09:04:00 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:55298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41154 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46754 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:46768 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34850 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 09:14:32 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:14:40.386 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 09:14:40.404 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 09:14:40.406 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [53567]
INFO:     Stopping reloader process [53564]
=== 2025-06-24 09:15:52 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [124719] using WatchFiles
INFO:     Started server process [124722]
INFO:     Waiting for application startup.
2025-06-24 09:15:55.118 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 09:15:55.119 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 09:15:55.120 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 09:15:55.406 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 09:15:55.407 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 09:15:55.866 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 09:15:57.799 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 09:15:57.800 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 09:15:57.866 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 09:15:57.867 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 09:15:58.103 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 09:15:58.104 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 09:15:58.106 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 09:15:58.525 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:15:58.527 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:15:58.527 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:15:58.528 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:15:58.529 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 09:15:58.529 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 09:15:58.530 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:46980 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51790 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57964 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33872 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41648 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33258 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41988 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45648 - "GET /health HTTP/1.1" 200 OK
2025-06-24 09:26:25.969 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:45658 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:26:26.086 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:45666 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:26:26.173 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:45676 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:26:26.233 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:45692 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:26:26.306 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:45704 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 09:26:26.371 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: column "permissions" of relation "roles" does not exist
INFO:     127.0.0.1:45706 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:26:41.055 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 09:26:41.358 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 09:26:41.359 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [124722]
INFO:     Stopping reloader process [124719]
=== 2025-06-24 09:26:51 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [130114] using WatchFiles
INFO:     Started server process [130117]
INFO:     Waiting for application startup.
2025-06-24 09:26:53.903 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 09:26:53.903 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 09:26:53.904 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 09:26:54.065 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 09:26:54.066 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 09:26:54.710 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 09:26:55.898 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 09:26:55.899 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 09:26:55.939 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 09:26:55.940 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 09:26:56.081 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 09:26:56.081 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 09:26:56.082 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 09:26:56.699 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:26:56.703 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:26:56.704 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:26:56.705 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:26:56.706 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 09:26:56.707 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 09:26:56.708 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:48276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47250 - "GET /health HTTP/1.1" 200 OK
2025-06-24 09:27:28.753 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:47260 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:27:28.812 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:47264 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:27:28.869 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:47278 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:27:28.949 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:47284 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:27:29.053 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:47286 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 09:27:29.132 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: column "permissions" of relation "roles" does not exist
INFO:     127.0.0.1:47302 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:41754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50244 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35636 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37950 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36898 - "GET /health HTTP/1.1" 200 OK
2025-06-24 09:38:09.569 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:36906 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:38:09.645 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:36908 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:38:09.714 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:36918 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:38:09.785 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:36932 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 09:38:09.909 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:36936 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 09:38:09.983 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:36938 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39392 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50686 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:42:47.229 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 09:42:47.287 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 09:42:47.288 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [130117]
INFO:     Stopping reloader process [130114]
=== 2025-06-24 09:42:59 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [135691] using WatchFiles
INFO:     Started server process [135695]
INFO:     Waiting for application startup.
2025-06-24 09:43:03.060 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 09:43:03.060 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 09:43:03.061 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 09:43:03.204 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 09:43:03.205 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 09:43:03.772 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 09:43:04.966 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 09:43:04.967 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 09:43:05.001 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 09:43:05.002 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 09:43:05.142 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 09:43:05.143 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 09:43:05.144 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 09:43:05.382 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:43:05.384 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:43:05.384 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:43:05.385 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:43:05.385 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 09:43:05.386 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 09:43:05.386 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:37252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:44:03.900 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 09:44:04.127 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 09:44:04.129 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [135695]
INFO:     Stopping reloader process [135691]
=== 2025-06-24 09:44:15 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [136116] using WatchFiles
INFO:     Started server process [136149]
INFO:     Waiting for application startup.
2025-06-24 09:44:19.016 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 09:44:19.016 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 09:44:19.017 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 09:44:19.187 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 09:44:19.188 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 09:44:19.623 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 09:44:21.028 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 09:44:21.028 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 09:44:21.064 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 09:44:21.064 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 09:44:21.187 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 09:44:21.188 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 09:44:21.188 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 09:44:21.514 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:44:21.518 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:44:21.519 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:44:21.520 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:44:21.521 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 09:44:21.522 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 09:44:21.523 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:45350 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53648 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42896 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46326 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41916 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:58:56.143 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 09:58:56.199 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 09:58:56.200 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [136149]
INFO:     Stopping reloader process [136116]
=== 2025-06-24 09:59:08 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [140541] using WatchFiles
INFO:     Started server process [140544]
INFO:     Waiting for application startup.
2025-06-24 09:59:10.555 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 09:59:10.556 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 09:59:10.556 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 09:59:10.780 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 09:59:10.781 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 09:59:11.516 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 09:59:12.740 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 09:59:12.741 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 09:59:12.774 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 09:59:12.774 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 09:59:12.922 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 09:59:12.923 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 09:59:12.924 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 09:59:13.164 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:59:13.165 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:59:13.165 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:59:13.166 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 09:59:13.166 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 09:59:13.166 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 09:59:13.167 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:60850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41242 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59676 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39472 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 10:13:34 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [11316] using WatchFiles
INFO:     Started server process [11319]
INFO:     Waiting for application startup.
2025-06-24 10:13:39.523 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 10:13:39.525 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 10:13:39.525 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 10:13:39.800 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 10:13:39.802 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 10:13:41.593 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 10:13:44.833 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 10:13:44.834 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 10:13:44.911 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 10:13:44.913 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 10:13:45.177 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 10:13:45.178 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 10:13:45.180 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 10:13:45.657 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:13:45.659 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:13:45.660 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:13:45.661 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:13:45.662 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 10:13:45.662 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 10:13:45.663 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:48366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52572 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:15:14.534 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:52588 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:15:14.592 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:52602 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:15:14.664 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:52608 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:15:14.747 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:52610 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:15:14.835 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:52626 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:15:14.911 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:52634 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:57094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44974 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51664 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58648 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40322 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51758 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:21:17.365 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:51768 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:17.409 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:51770 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:17.449 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:51780 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:17.486 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:51788 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:17.544 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:51804 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:21:17.582 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:51818 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:51030 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:21:36.804 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:51046 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:36.845 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:51062 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:36.888 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:51064 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:36.936 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:51068 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:37.038 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:51078 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:21:37.121 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:51088 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:40334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34070 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:21:55.259 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:34076 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:55.324 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:34084 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:55.410 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:34098 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:55.488 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:34102 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:21:55.576 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:34116 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:21:55.621 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:34124 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36106 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:22:11.544 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:36120 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:22:11.586 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:36122 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:22:11.625 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:36126 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:22:11.667 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:36142 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:22:11.727 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:36148 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:22:11.775 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:36152 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:60646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52762 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:23:48.376 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:52770 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:23:48.418 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:52782 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:23:48.459 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:52788 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:23:48.498 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:52798 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:23:48.547 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:52810 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:23:48.585 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: invalid input for query argument $6: ['*:*'] (expected str, got list)
INFO:     127.0.0.1:52822 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:34844 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35436 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:35448 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36552 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60270 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49968 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49974 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:33:42.933 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:49982 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:33:43.008 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:49992 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:33:43.080 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:50004 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:33:43.152 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:50008 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:33:43.241 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:50010 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:33:43.330 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:50012 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:50466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50482 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:34:15.786 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:50490 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:34:15.832 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:50504 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:34:15.884 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:50508 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:34:15.924 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:50524 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:34:15.972 | INFO     | main:select_records:335 - 📥 Selected 1 records from roles
INFO:     127.0.0.1:50526 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:34:16.014 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:50530 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:57142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 10:35:20.580 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 10:35:20.592 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 10:35:20.592 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [11319]
INFO:     Stopping reloader process [11316]
=== 2025-06-24 10:35:30 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [18025] using WatchFiles
INFO:     Started server process [18028]
INFO:     Waiting for application startup.
2025-06-24 10:35:33.482 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 10:35:33.483 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 10:35:33.483 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 10:35:33.639 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 10:35:33.640 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 10:35:34.380 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 10:35:35.692 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 10:35:35.693 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 10:35:35.726 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 10:35:35.726 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 10:35:35.886 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 10:35:35.887 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 10:35:35.888 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 10:35:36.173 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:35:36.175 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:35:36.175 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:35:36.176 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:35:36.176 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 10:35:36.176 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 10:35:36.177 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35574 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39636 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41980 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:39:05.249 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:41990 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:05.305 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:42002 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:05.353 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:42008 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:05.420 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:42014 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
INFO:     127.0.0.1:52010 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34778 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:39:28.926 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:34792 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:28.966 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:34804 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:29.014 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:34816 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:29.064 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:34820 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
INFO:     127.0.0.1:54368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54370 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:39:43.849 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:54380 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:43.883 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:54394 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:43.918 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:54406 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:39:43.952 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:54416 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39796 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:41:37.190 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:39798 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:41:37.246 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:39808 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:41:37.326 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:39820 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:41:37.413 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:39830 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:41:37.521 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:39844 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:41:37.606 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:39858 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:59640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50990 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:42:06.507 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:51004 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:06.556 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:51012 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:06.608 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:51014 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:06.656 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:51020 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:06.708 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:51032 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:42:06.782 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:51038 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:33122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59570 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:42:43.782 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:59586 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:43.820 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:59592 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:43.855 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:59602 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:43.888 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:59618 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:42:43.932 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:59632 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:42:43.966 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:59646 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 10:43:05.396 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 10:43:05.410 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 10:43:05.410 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [18028]
INFO:     Stopping reloader process [18025]
=== 2025-06-24 10:43:16 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [20325] using WatchFiles
INFO:     Started server process [20330]
INFO:     Waiting for application startup.
2025-06-24 10:43:19.969 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 10:43:19.970 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 10:43:19.970 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 10:43:20.174 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 10:43:20.175 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 10:43:20.659 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 10:43:22.022 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 10:43:22.024 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 10:43:22.062 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 10:43:22.063 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 10:43:22.258 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 10:43:22.259 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 10:43:22.260 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 10:43:22.718 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:43:22.721 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:43:22.722 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:43:22.723 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 10:43:22.724 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 10:43:22.725 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 10:43:22.725 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:35858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49112 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:45:19.657 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:49124 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:19.725 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:49134 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:19.780 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:49136 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:19.873 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:49146 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:19.984 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:49158 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:45:20.106 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:49168 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:57704 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:45:39.100 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:57716 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:39.142 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:57720 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:39.185 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:57724 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:39.244 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:57730 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:39.316 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:57732 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:45:39.372 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:57734 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:58270 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57306 - "GET /health HTTP/1.1" 200 OK
2025-06-24 10:45:55.423 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:57322 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:55.466 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:57332 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:55.507 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:57346 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:55.544 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:57348 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 10:45:55.589 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:57358 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 10:45:55.624 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:57362 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:50324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52190 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47382 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36524 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57374 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46404 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43698 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51772 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41286 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     127.0.0.1:43136 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55280 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51066 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51068 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34220 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44140 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59822 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33654 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47710 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44214 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50204 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42686 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49710 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52350 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60644 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40964 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56728 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40190 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37948 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59152 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41930 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33672 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43692 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59070 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 12:35:02 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [22173] using WatchFiles
INFO:     Started server process [22204]
INFO:     Waiting for application startup.
2025-06-24 12:35:08.348 | INFO     | main:startup_event:394 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 12:35:08.349 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 12:35:08.349 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 12:35:08.680 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 12:35:08.682 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 12:35:09.906 | INFO     | main:startup_event:419 - 🔧 Starting database initialization...
2025-06-24 12:35:12.416 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 12:35:12.417 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 12:35:12.500 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 12:35:12.501 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 12:35:12.756 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 12:35:12.757 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 12:35:12.762 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 12:35:13.259 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 12:35:13.261 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 12:35:13.261 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 12:35:13.262 | WARNING  | main:startup_event:429 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 12:35:13.263 | INFO     | main:startup_event:430 - 🔄 Service will continue in basic mode...
2025-06-24 12:35:13.263 | INFO     | main:startup_event:443 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 12:35:13.264 | INFO     | main:startup_event:444 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:59376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52982 - "GET /health HTTP/1.1" 200 OK
2025-06-24 12:36:53.843 | INFO     | main:create_table:212 - ✅ Created table: users
INFO:     127.0.0.1:52996 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 12:36:53.916 | INFO     | main:create_table:212 - ✅ Created table: user_profiles
INFO:     127.0.0.1:53010 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 12:36:53.983 | INFO     | main:create_table:212 - ✅ Created table: roles
INFO:     127.0.0.1:53012 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 12:36:54.060 | INFO     | main:create_table:212 - ✅ Created table: user_roles
INFO:     127.0.0.1:53024 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 12:36:54.170 | INFO     | main:select_records:335 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:53034 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 12:36:54.236 | ERROR    | main:insert_record:302 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:53046 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:46146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37930 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:41418 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49382 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54180 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36158 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33362 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43882 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38312 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49282 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40600 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41552 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55520 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49540 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51290 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39636 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40680 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52024 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51884 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60218 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52254 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54454 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43844 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41856 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49064 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43990 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37078 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41362 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57242 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37258 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38374 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47190 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33006 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 13:33:03.348 | INFO     | main:shutdown_event:495 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 13:33:03.367 | INFO     | main:shutdown_event:499 - ✅ PostgreSQL connection pool closed
2025-06-24 13:33:03.369 | INFO     | main:shutdown_event:501 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [22204]
INFO:     Started server process [38652]
INFO:     Waiting for application startup.
2025-06-24 13:33:07.398 | INFO     | main:startup_event:404 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 13:33:07.399 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 13:33:07.399 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 13:33:07.559 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 13:33:07.561 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 13:33:08.045 | INFO     | main:startup_event:429 - 🔧 Starting database initialization...
2025-06-24 13:33:09.355 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 13:33:09.356 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 13:33:09.429 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 13:33:09.430 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 13:33:09.660 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 13:33:09.661 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 13:33:09.663 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 13:33:10.026 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:10.028 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:10.029 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:10.029 | WARNING  | main:startup_event:439 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:10.030 | INFO     | main:startup_event:440 - 🔄 Service will continue in basic mode...
2025-06-24 13:33:10.031 | INFO     | main:startup_event:453 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 13:33:10.031 | INFO     | main:startup_event:454 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:58654 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 13:33:31.309 | INFO     | main:shutdown_event:505 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 13:33:31.322 | INFO     | main:shutdown_event:509 - ✅ PostgreSQL connection pool closed
2025-06-24 13:33:31.323 | INFO     | main:shutdown_event:511 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [38652]
INFO:     Started server process [38792]
INFO:     Waiting for application startup.
2025-06-24 13:33:34.399 | INFO     | main:startup_event:511 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 13:33:34.399 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 13:33:34.400 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 13:33:34.516 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 13:33:34.517 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 13:33:35.268 | INFO     | main:startup_event:536 - 🔧 Starting database initialization...
2025-06-24 13:33:36.572 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 13:33:36.573 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 13:33:36.598 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 13:33:36.599 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 13:33:36.759 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 13:33:36.760 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 13:33:36.761 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 13:33:37.032 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:37.033 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:37.034 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:37.034 | WARNING  | main:startup_event:546 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:37.035 | INFO     | main:startup_event:547 - 🔄 Service will continue in basic mode...
2025-06-24 13:33:37.035 | INFO     | main:startup_event:560 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 13:33:37.035 | INFO     | main:startup_event:561 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:49980 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 13:33:48.980 | INFO     | main:shutdown_event:612 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 13:33:48.993 | INFO     | main:shutdown_event:616 - ✅ PostgreSQL connection pool closed
2025-06-24 13:33:48.994 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [38792]
INFO:     Started server process [38848]
INFO:     Waiting for application startup.
2025-06-24 13:33:51.829 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 13:33:51.829 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 13:33:51.830 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
2025-06-24 13:33:51.945 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for postgresql-cluster
2025-06-24 13:33:51.947 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for postgresql-cluster, using environment variables
2025-06-24 13:33:52.376 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 13:33:53.783 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 13:33:53.784 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 13:33:53.889 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 13:33:53.899 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 13:33:54.100 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 13:33:54.100 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 13:33:54.102 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 13:33:54.435 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:54.437 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:54.438 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:54.438 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 13:33:54.439 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 13:33:54.440 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 13:33:54.440 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:42720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49952 - "GET /health HTTP/1.1" 200 OK
2025-06-24 13:34:23.822 | INFO     | main:create_table:224 - ✅ Created table: users
INFO:     127.0.0.1:49962 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:34:23.906 | INFO     | main:create_table:224 - ✅ Created table: user_profiles
INFO:     127.0.0.1:49964 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:34:23.983 | INFO     | main:create_table:224 - ✅ Created table: roles
INFO:     127.0.0.1:49974 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:34:24.052 | INFO     | main:create_table:224 - ✅ Created table: user_roles
INFO:     127.0.0.1:49982 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:34:24.132 | INFO     | main:select_records:347 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:49986 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 13:34:24.203 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:49994 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:58912 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56256 - "GET /health HTTP/1.1" 200 OK
2025-06-24 13:35:19.131 | INFO     | main:create_table:224 - ✅ Created table: users
INFO:     127.0.0.1:56270 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:35:19.178 | INFO     | main:create_table:224 - ✅ Created table: user_profiles
INFO:     127.0.0.1:56274 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:35:19.222 | INFO     | main:create_table:224 - ✅ Created table: roles
INFO:     127.0.0.1:56278 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:35:19.265 | INFO     | main:create_table:224 - ✅ Created table: user_roles
INFO:     127.0.0.1:56284 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:35:19.318 | INFO     | main:select_records:347 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:56296 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 13:35:19.369 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:56304 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:42440 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36574 - "GET /health HTTP/1.1" 200 OK
2025-06-24 13:36:38.640 | INFO     | main:create_table:224 - ✅ Created table: users
INFO:     127.0.0.1:36582 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:38.726 | INFO     | main:create_table:224 - ✅ Created table: user_profiles
INFO:     127.0.0.1:36590 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:38.860 | INFO     | main:create_table:224 - ✅ Created table: roles
INFO:     127.0.0.1:36600 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:38.945 | INFO     | main:create_table:224 - ✅ Created table: user_roles
INFO:     127.0.0.1:36608 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:39.091 | INFO     | main:select_records:347 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:36618 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 13:36:39.198 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:36628 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47476 - "GET /health HTTP/1.1" 200 OK
2025-06-24 13:36:55.227 | INFO     | main:create_table:224 - ✅ Created table: users
INFO:     127.0.0.1:47478 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:55.359 | INFO     | main:create_table:224 - ✅ Created table: user_profiles
INFO:     127.0.0.1:47480 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:55.462 | INFO     | main:create_table:224 - ✅ Created table: roles
INFO:     127.0.0.1:47490 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:55.533 | INFO     | main:create_table:224 - ✅ Created table: user_roles
INFO:     127.0.0.1:47494 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:36:55.636 | INFO     | main:select_records:347 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:47496 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 13:36:55.711 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:47508 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:35778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48000 - "GET /health HTTP/1.1" 200 OK
2025-06-24 13:37:15.792 | INFO     | main:create_table:224 - ✅ Created table: users
INFO:     127.0.0.1:48014 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:37:15.850 | INFO     | main:create_table:224 - ✅ Created table: user_profiles
INFO:     127.0.0.1:48024 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:37:15.915 | INFO     | main:create_table:224 - ✅ Created table: roles
INFO:     127.0.0.1:48032 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:37:15.970 | INFO     | main:create_table:224 - ✅ Created table: user_roles
INFO:     127.0.0.1:48046 - "POST /api/v1/tables/create HTTP/1.1" 200 OK
2025-06-24 13:37:16.040 | INFO     | main:select_records:347 - 📥 Selected 5 records from roles
INFO:     127.0.0.1:48060 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 13:37:16.087 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into roles: duplicate key value violates unique constraint "roles_name_key"
DETAIL:  Key (name)=(super_admin) already exists.
INFO:     127.0.0.1:48074 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:40076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50770 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 13:39:50.072 | ERROR    | main:authenticate_user:454 - ❌ Failed to authenticate user: column "password_hash" does not exist
INFO:     127.0.0.1:39684 - "POST /api/v1/users/authenticate HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:37450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36728 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 13:40:43.075 | ERROR    | main:authenticate_user:454 - ❌ Failed to authenticate user: column "password_hash" does not exist
INFO:     127.0.0.1:54888 - "POST /api/v1/users/authenticate HTTP/1.1" 500 Internal Server Error
2025-06-24 13:41:05.054 | ERROR    | main:authenticate_user:454 - ❌ Failed to authenticate user: column "password_hash" does not exist
INFO:     127.0.0.1:50690 - "POST /api/v1/users/authenticate HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:50700 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 13:41:23.001 | ERROR    | main:authenticate_user:454 - ❌ Failed to authenticate user: column "password_hash" does not exist
INFO:     127.0.0.1:35580 - "POST /api/v1/users/authenticate HTTP/1.1" 500 Internal Server Error
2025-06-24 13:41:32.944 | ERROR    | main:authenticate_user:454 - ❌ Failed to authenticate user: column "password_hash" does not exist
INFO:     127.0.0.1:41692 - "POST /api/v1/users/authenticate HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:41706 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60532 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:42288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49210 - "GET /metrics HTTP/1.1" 404 Not Found
