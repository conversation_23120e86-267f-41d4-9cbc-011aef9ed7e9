=== 2025-06-23 20:20:18 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [88130] using WatchFiles
INFO:     Started server process [88143]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-23 20:20:25.594 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:42648 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:57654 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:55914 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:55930 - "GET /favicon.ico HTTP/1.1" 404 Not Found
=== 2025-06-23 21:01:24 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [11420] using WatchFiles
INFO:     Started server process [11423]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-23 21:01:30.754 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:58372 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:60218 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:58984 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:58996 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46108 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:47832 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:47616 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
=== 2025-06-24 06:27:27 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Service Registry...
2025-06-24 06:27:49.266 | INFO     | app.service_registry:stop:72 - 🛑 Service Registry stopped
INFO:main:✅ Service Registry shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [11423]
INFO:     Stopping reloader process [11420]
=== 2025-06-24 09:03:15 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [118874] using WatchFiles
INFO:     Started server process [118878]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 09:03:26.932 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:49826 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:38498 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 09:13:54 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Service Registry...
2025-06-24 09:14:42.049 | INFO     | app.service_registry:stop:72 - 🛑 Service Registry stopped
INFO:main:✅ Service Registry shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [118878]
INFO:     Stopping reloader process [118874]
=== 2025-06-24 09:15:26 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [124554] using WatchFiles
INFO:     Started server process [124557]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 09:15:29.582 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:34246 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:41972 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:52014 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:53786 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
=== 2025-06-24 10:12:52 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [10962] using WatchFiles
INFO:     Started server process [10965]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 10:13:00.824 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:41584 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56342 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:55142 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:49954 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:60968 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:55378 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56684 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:50768 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:33372 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:58806 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:49614 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:45318 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:52592 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:52370 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:60636 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46594 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:48744 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:53626 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:59134 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:33716 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:40216 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:34740 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:48230 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:48222 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:58678 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
=== 2025-06-24 12:34:16 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [21917] using WatchFiles
INFO:     Started server process [21921]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 12:34:25.936 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:44158 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:41026 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:37630 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:35450 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:34012 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:35696 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:41274 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:48426 - "POST /api/v1/registry/register HTTP/1.1" 401 Unauthorized
