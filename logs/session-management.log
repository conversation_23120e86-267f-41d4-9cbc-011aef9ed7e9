=== 2025-06-24 09:07:50 - Starting session-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/session-management']
INFO:     Uvicorn running on http://0.0.0.0:7302 (Press CTRL+C to quit)
INFO:     Started reloader process [120386] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [120389]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:46164 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [120389]
INFO:     Stopping reloader process [120386]
=== 2025-06-24 09:56:59 - Starting session-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/session-management']
INFO:     Uvicorn running on http://0.0.0.0:7302 (Press CTRL+C to quit)
INFO:     Started reloader process [139850] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [139853]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
=== 2025-06-24 10:15:19 - Starting session-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/session-management']
INFO:     Uvicorn running on http://0.0.0.0:7302 (Press CTRL+C to quit)
INFO:     Started reloader process [12169] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [12173]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:55620 - "GET / HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/services.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [12173]
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/api.py', 'app/services/business/session_management/schemas.py', 'app/services/business/session_management/config.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/redis_client.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/auth.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [14130]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/api.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [14130]
WARNING:main:Service-specific routes not found
INFO:     Started server process [23882]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/schemas.py', 'app/services/business/session_management/services.py', 'app/services/business/session_management/auth.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [23882]
WARNING:main:Service-specific routes not found
INFO:     Started server process [23891]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/config.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [23891]
WARNING:main:Service-specific routes not found
INFO:     Started server process [38503]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/session_management/redis_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [38503]
WARNING:main:Service-specific routes not found
INFO:     Started server process [38607]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
=== 2025-06-24 12:36:58 - Starting session-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/session-management']
INFO:     Uvicorn running on http://0.0.0.0:7302 (Press CTRL+C to quit)
INFO:     Started reloader process [22960] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [22964]
INFO:     Waiting for application startup.
INFO:main:✅ Vault integration initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:55526 - "GET / HTTP/1.1" 200 OK
