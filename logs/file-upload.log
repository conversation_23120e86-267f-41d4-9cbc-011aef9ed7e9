=== 2025-06-24 09:09:23 - Starting file-upload ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/file-upload']
INFO:     Uvicorn running on http://0.0.0.0:7014 (Press CTRL+C to quit)
INFO:     Started reloader process [121092] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
INFO:     Stopping reloader process [121092]
=== 2025-06-24 09:57:25 - Starting file-upload ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/file-upload']
INFO:     Uvicorn running on http://0.0.0.0:7014 (Press CTRL+C to quit)
INFO:     Started reloader process [139950] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
=== 2025-06-24 10:15:29 - Starting file-upload ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/file-upload']
INFO:     Uvicorn running on http://0.0.0.0:7014 (Press CTRL+C to quit)
INFO:     Started reloader process [12197] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
=== 2025-06-24 12:37:10 - Starting file-upload ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/file-upload']
INFO:     Uvicorn running on http://0.0.0.0:7014 (Press CTRL+C to quit)
INFO:     Started reloader process [23051] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/file-upload/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
