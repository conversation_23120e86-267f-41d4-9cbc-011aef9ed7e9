=== 2025-06-23 09:34:09 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7009 (Press CTRL+C to quit)
INFO:     Started reloader process [142584] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
INFO:     Stopping reloader process [142584]
INFO:     Stopping reloader process [135225]
INFO:     Stopping reloader process [119715]
INFO:     Stopping reloader process [139413]
INFO:     Stopping reloader process [118094]
=== 2025-06-23 12:22:48 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7009 (Press CTRL+C to quit)
INFO:     Started reloader process [14594] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
WARNING:  WatchFiles detected changes in 'app/services/email_notification_service.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
WARNING:  WatchFiles detected changes in 'app/services/email_notification_service.py'. Reloading...
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
=== 2025-06-23 15:37:54 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7009 (Press CTRL+C to quit)
INFO:     Started reloader process [15225] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
=== 2025-06-23 18:22:16 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7009 (Press CTRL+C to quit)
INFO:     Started reloader process [15155] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 15, in <module>
    from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/startup/vault_startup.py", line 15, in <module>
    from vault_integration import VaultIntegration, inject_vault_config_to_env
  File "/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul/app/vault_integration.py", line 17, in <module>
    from app.vault_client import vault_client
ModuleNotFoundError: No module named 'app.vault_client'
WARNING:  WatchFiles detected changes in 'app/startup/vault_startup.py', 'app/services/inter_service_client.py', 'app/api/endpoints.py', 'main.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [63623]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-23 19:56:40.541 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-23 19:56:40.542 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-23 19:56:40.775 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-23 19:56:40.777 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
WARNING:main:Service initialization failed: No module named 'inter_service_client'
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63623]
INFO:     Stopping reloader process [15155]
=== 2025-06-23 20:05:27 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7009 (Press CTRL+C to quit)
INFO:     Started reloader process [72447] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [72452]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-23 20:05:32.063 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-23 20:05:32.064 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-23 20:05:32.225 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-23 20:05:32.227 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-23 20:05:35.975 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: [Errno -2] Name or service not known
2025-06-23 20:05:35.977 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:42110 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72452]
INFO:     Stopping reloader process [72447]
=== 2025-06-23 20:22:55 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [89175] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [89181]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-23 20:23:02.500 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-23 20:23:02.500 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-23 20:23:02.743 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-23 20:23:02.745 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-23 20:23:02.918 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-23 20:23:02.919 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:59646 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:35194 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:35198 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60790 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:54844 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:51394 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:51394 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:41652 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:41660 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59414 - "GET /billing/subscribe HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59426 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47778 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:47778 - "GET /openapi.json HTTP/1.1" 200 OK
=== 2025-06-23 21:03:25 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [12258] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [12261]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-23 21:03:28.703 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-23 21:03:28.703 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-23 21:03:28.833 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-23 21:03:28.834 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-23 21:03:28.881 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-23 21:03:28.882 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:57758 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:40396 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:40396 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:46578 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:46578 - "GET /openapi.json HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [12261]
WARNING:main:Service-specific routes not found
INFO:     Started server process [47179]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:22:52.620 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:22:52.621 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:22:52.896 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:22:52.899 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 06:22:53.103 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-24 06:22:53.105 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/payment_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47179]
WARNING:main:Service-specific routes not found
INFO:     Started server process [47234]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:22:59.704 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:22:59.705 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:22:59.881 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:22:59.884 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 06:22:59.958 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-24 06:22:59.960 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47234]
WARNING:main:Service-specific routes not found
INFO:     Started server process [47308]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:23:22.316 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:23:22.318 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:23:22.545 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:23:22.548 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 06:23:22.645 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-24 06:23:22.646 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/services/payment_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47308]
WARNING:main:Service-specific routes not found
INFO:     Started server process [47368]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:23:33.127 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:23:33.127 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:23:33.284 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:23:33.286 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 06:23:33.340 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-24 06:23:33.342 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47368]
WARNING:main:Service-specific routes not found
INFO:     Started server process [47385]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:23:45.656 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:23:45.657 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:23:45.835 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:23:45.836 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 06:23:45.905 | WARNING  | app.services.inter_service_client:_register_billing_service:88 - ⚠️ Failed to register with service registry: 401
2025-06-24 06:23:45.906 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [47385]
INFO:     Stopping reloader process [12258]
=== 2025-06-24 06:30:03 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [54429] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [54432]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:30:09.099 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:30:09.099 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:30:09.319 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:30:09.322 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:30:09.384 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:30:09.388 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:43852 - "GET / HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/billing/models.py', 'app/services/business/billing/services/payment_manager.py', 'app/services/subscription_manager.py', 'app/services/business/billing/subscription_manager.py', 'app/services/services/payment_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [54432]
WARNING:main:Service-specific routes not found
INFO:     Started server process [55656]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:35:15.527 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:35:15.528 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:35:15.656 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:35:15.657 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:35:15.693 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:35:15.694 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/payment_providers/mpesa.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55656]
WARNING:main:Service-specific routes not found
INFO:     Started server process [58890]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:48:52.644 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:48:52.645 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:48:52.782 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:48:52.783 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:48:52.823 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:48:52.826 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/payment_providers/paypal.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58890]
WARNING:  WatchFiles detected changes in 'app/payment_providers/stripe.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [58991]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:49:14.460 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:49:14.460 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:49:14.653 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:49:14.655 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:49:14.715 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:49:14.719 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58991]
WARNING:main:Service-specific routes not found
INFO:     Started server process [59376]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:50:54.603 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:50:54.604 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:50:54.797 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:50:54.800 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:50:54.851 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:50:54.854 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/services/subscription.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59376]
WARNING:main:Service-specific routes not found
INFO:     Started server process [59528]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:51:28.638 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:51:28.639 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:51:28.819 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:51:28.821 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:51:28.880 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:51:28.882 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59528]
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [59552]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:51:41.451 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:51:41.451 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:51:41.590 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:51:41.591 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:51:41.633 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:51:41.636 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59552]
WARNING:main:Service-specific routes not found
INFO:     Started server process [59711]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:52:15.711 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:52:15.712 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:52:15.877 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:52:15.879 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:52:15.922 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:52:15.924 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/base.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59711]
WARNING:main:Service-specific routes not found
INFO:     Started server process [59969]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:53:28.827 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:53:28.827 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:53:29.003 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:53:29.006 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:53:29.075 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:53:29.079 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/credit_card.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59969]
WARNING:main:Service-specific routes not found
INFO:     Started server process [60070]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:53:57.147 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:53:57.148 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:53:57.282 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:53:57.284 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:53:57.325 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:53:57.327 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/mpesa.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60070]
WARNING:main:Service-specific routes not found
INFO:     Started server process [60329]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:54:57.543 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:54:57.543 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:54:57.698 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:54:57.700 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:54:57.753 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:54:57.755 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/paypal.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60329]
WARNING:main:Service-specific routes not found
INFO:     Started server process [60489]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:55:51.534 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:55:51.534 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:55:51.711 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:55:51.713 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:55:51.774 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:55:51.777 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60489]
WARNING:main:Service-specific routes not found
INFO:     Started server process [60559]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:56:11.866 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:56:11.867 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:56:12.104 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:56:12.107 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:56:12.165 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:56:12.168 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60559]
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/stripe.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [60632]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:56:24.141 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:56:24.142 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:56:24.311 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:56:24.314 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:56:24.369 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:56:24.371 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/__init__.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60632]
WARNING:main:Service-specific routes not found
INFO:     Started server process [60692]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 06:56:35.757 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 06:56:35.758 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 06:56:35.887 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 06:56:35.888 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 06:56:35.931 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 06:56:35.933 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/base.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [60692]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63103]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:04:04.869 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:04:04.870 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:04:05.063 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:04:05.064 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:04:05.117 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:04:05.120 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/mpesa_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63103]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63130]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:04:18.713 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:04:18.713 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:04:18.859 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:04:18.861 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:04:18.907 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:04:18.909 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/payment.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63130]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63566]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:06:03.229 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:06:03.230 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:06:03.375 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:06:03.376 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:06:03.432 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:06:03.435 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/payment_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63566]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63584]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:06:17.973 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:06:17.974 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:06:18.117 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:06:18.119 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:06:18.164 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:06:18.166 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/paypal_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63584]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63718]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:06:48.602 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:06:48.603 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:06:48.763 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:06:48.765 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:06:48.817 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:06:48.821 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63718]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63791]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:07:02.908 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:07:02.909 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:07:03.070 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:07:03.071 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:07:03.121 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:07:03.124 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/subscription.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63791]
WARNING:main:Service-specific routes not found
INFO:     Started server process [63976]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:07:55.628 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:07:55.628 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:07:55.783 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:07:55.784 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:07:55.832 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:07:55.835 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/mpesa_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [63976]
WARNING:main:Service-specific routes not found
INFO:     Started server process [64052]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:08:21.292 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:08:21.293 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:08:21.516 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:08:21.517 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:08:21.570 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:08:21.573 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/paypal_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [64052]
WARNING:main:Service-specific routes not found
INFO:     Started server process [64116]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:08:28.395 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:08:28.396 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:08:28.585 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:08:28.586 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:08:28.655 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:08:28.660 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [64116]
WARNING:main:Service-specific routes not found
INFO:     Started server process [64174]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:08:35.186 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:08:35.187 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:08:35.323 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:08:35.324 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:08:35.367 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:08:35.370 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/services/mpesa_service.py', 'app/services/payment_providers/credit_card.py', 'app/services/services/subscription.py', 'app/services/services/payment.py', 'app/services/business/billing/services/subscription.py', 'app/services/payment_providers/stripe_service.py', 'app/services/business/billing/payment_providers/stripe_service.py', 'app/services/services/base.py', 'app/api/endpoints.py', 'app/services/payment_providers/stripe.py', 'app/services/services/stripe_service.py', 'app/services/payment_providers/__init__.py', 'app/services/payment_providers/paypal.py', 'app/services/services/payment_manager.py', 'app/services/payment_providers/base.py', 'app/services/services/paypal_service.py', 'app/services/payment_providers/mpesa.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [64174]
WARNING:main:Service-specific routes not found
INFO:     Started server process [65085]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:11:37.413 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:11:37.413 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:11:37.530 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:11:37.532 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:11:37.565 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:11:37.567 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [65085]
WARNING:main:Service-specific routes not found
INFO:     Started server process [68772]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:26:53.254 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:26:53.255 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:26:53.396 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:26:53.398 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:26:53.445 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:26:53.448 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [68772]
WARNING:main:Service-specific routes not found
INFO:     Started server process [68836]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:27:14.956 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:27:14.957 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:27:15.195 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:27:15.196 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:27:15.275 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:27:15.279 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_role_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [68836]
WARNING:main:Service-specific routes not found
INFO:     Started server process [68879]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:27:24.952 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:27:24.952 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:27:25.111 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:27:25.112 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:27:25.163 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:27:25.167 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/usage_tracker.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [68879]
WARNING:main:Service-specific routes not found
INFO:     Started server process [68892]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:27:31.076 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:27:31.077 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:27:31.224 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:27:31.225 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:27:31.272 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:27:31.274 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_role_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [68892]
WARNING:main:Service-specific routes not found
INFO:     Started server process [69021]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:27:53.701 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:27:53.701 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:27:53.950 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:27:53.951 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:27:54.005 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:27:54.008 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69021]
WARNING:main:Service-specific routes not found
INFO:     Started server process [69035]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:28:00.857 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:28:00.858 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:28:00.986 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:28:00.987 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:28:01.031 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:28:01.033 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69035]
WARNING:main:Service-specific routes not found
INFO:     Started server process [69100]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:28:09.145 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:28:09.147 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:28:09.320 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:28:09.322 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:28:09.375 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:28:09.379 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/usage_tracker.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69100]
WARNING:main:Service-specific routes not found
INFO:     Started server process [69120]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:28:16.829 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:28:16.830 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:28:16.971 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:28:16.973 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:28:17.017 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:28:17.019 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69120]
WARNING:main:Service-specific routes not found
INFO:     Started server process [72186]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:41:57.404 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:41:57.404 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:41:57.517 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:41:57.519 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:41:57.556 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:41:57.558 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72186]
WARNING:  WatchFiles detected changes in 'app/services/subscription_role_manager.py', 'app/services/usage_tracker.py', 'app/services/models.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [72240]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:42:09.792 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:42:09.793 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:42:09.934 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:42:09.936 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:42:09.989 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:42:09.991 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/usage_tracker.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72240]
WARNING:main:Service-specific routes not found
INFO:     Started server process [72309]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:42:20.620 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:42:20.620 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:42:20.787 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:42:20.789 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:42:20.855 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:42:20.857 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_role_manager.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72309]
WARNING:main:Service-specific routes not found
INFO:     Started server process [72331]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:42:29.447 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:42:29.447 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:42:29.586 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:42:29.588 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:42:29.634 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:42:29.636 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72331]
WARNING:main:Service-specific routes not found
INFO:     Started server process [72387]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:42:41.255 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:42:41.256 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:42:41.391 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:42:41.393 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:42:41.460 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:42:41.463 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [72387]
WARNING:main:Service-specific routes not found
INFO:     Started server process [74216]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:48:41.651 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:48:41.652 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:48:41.778 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:48:41.780 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:48:41.822 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:48:41.825 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [74216]
WARNING:main:Service-specific routes not found
INFO:     Started server process [74750]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:50:59.717 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:50:59.718 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:50:59.889 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:50:59.891 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:50:59.955 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:50:59.957 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [74750]
WARNING:main:Service-specific routes not found
INFO:     Started server process [74841]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:51:19.639 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:51:19.640 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:51:19.833 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:51:19.836 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:51:19.891 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:51:19.893 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [74841]
INFO:     Stopping reloader process [54429]
=== 2025-06-24 07:51:50 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [75015] using WatchFiles
WARNING:main:Service-specific routes not found
INFO:     Started server process [75020]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:51:55.079 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:51:55.080 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:51:55.274 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:51:55.275 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:51:55.340 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:51:55.342 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:47010 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:47026 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50848 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:50848 - "GET /openapi.json HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75020]
WARNING:main:Service-specific routes not found
INFO:     Started server process [76469]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:58:47.276 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:58:47.276 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:58:47.518 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:58:47.520 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:58:47.573 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:58:47.575 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [76469]
WARNING:main:Service-specific routes not found
INFO:     Started server process [76531]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 07:58:56.429 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 07:58:56.429 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 07:58:56.601 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 07:58:56.603 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 07:58:56.662 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 07:58:56.665 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [76531]
WARNING:main:Service-specific routes not found
INFO:     Started server process [95083]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 08:47:18.735 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 08:47:18.736 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 08:47:18.909 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 08:47:18.910 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 08:47:18.954 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 08:47:18.956 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py', 'app/services/subscription_role_manager.py', 'app/services/models.py', 'app/services/usage_tracker.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [95083]
WARNING:main:Service-specific routes not found
INFO:     Started server process [96799]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 08:49:05.183 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 08:49:05.185 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 08:49:05.390 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 08:49:05.391 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 08:49:05.431 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 08:49:05.433 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/credit_card.py', 'app/services/business/billing/services/stripe_service.py', 'app/api/routes/subscriptions.py', 'app/services/subscription_manager.py', 'app/api/dependencies.py', 'app/services/business/billing/subscription_role_manager.py', 'main.py', 'app/services/services/payment_manager.py', 'app/services/business/billing/subscription_manager.py', 'app/services/business/billing/payment_providers/mpesa.py', 'app/services/services/subscription.py', 'app/services/business/billing/schemas/payment.py', 'app/services/services/payment.py', 'app/webhooks/payment_webhooks.py', 'app/services/business/billing/__init__.py', 'app/services/business/billing/payment_providers/__init__.py', 'app/services/services/paypal_service.py', 'app/api/routes/webhooks.py', 'app/api/v1/analytics.py', 'app/services/business/billing/receipt_generator.py', 'app/services/business/billing/services/payment_manager.py', 'app/services/business/billing/payment_providers/paypal.py', 'app/services/business/billing/schemas/base.py', 'app/services/schemas/__init__.py', 'app/services/models/subscription.py', 'app/services/models/payment.py', 'app/services/business/billing/usage_tracker.py', 'app/services/models/base.py', 'app/services/business/billing/services/paypal_service.py', 'app/services/schemas/payment.py', 'app/services/schemas/subscription.py', 'app/services/inter_service_client.py', 'app/services/usage_tracker.py', 'app/services/services/mpesa_service.py', 'app/services/services/base.py', 'app/services/models/enums.py', 'app/services/enhanced_plan_management_service.py', 'app/models/database.py', 'app/services/business/billing/services/payment.py', 'app/services/business/billing/exceptions.py', 'app/services/business/billing/models.py', 'app/api/__init__.py', 'app/services/business/billing/payment_providers/base.py', 'app/services/business/billing/services/mpesa_service.py', 'app/services/business/billing/services/base.py', 'app/services/services/stripe_service.py', 'app/services/exceptions.py', 'app/services/business/billing/schemas/__init__.py', 'app/services/models.py', 'app/api/v1/plans_management.py', 'app/services/schemas/base.py', 'app/api/routes/payments/endpoints.py', 'app/api/routes/billing.py', 'app/services/business/billing/payment_providers/stripe.py', 'app/main.py', 'app/services/subscription_role_manager.py', 'app/services/business/billing/schemas/subscription.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [96799]
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 17, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 15, in <module>
    from app.api.dependencies import get_current_user
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/dependencies.py", line 10, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/credit_card.py', 'app/services/business/billing/services/stripe_service.py', 'app/api/routes/subscriptions.py', 'app/services/subscription_manager.py', 'app/api/dependencies.py', 'app/services/business/billing/subscription_role_manager.py', 'main.py', 'app/services/services/payment_manager.py', 'app/services/business/billing/subscription_manager.py', 'app/services/business/billing/payment_providers/mpesa.py', 'app/services/services/subscription.py', 'app/services/business/billing/schemas/payment.py', 'app/services/services/payment.py', 'app/webhooks/payment_webhooks.py', 'app/services/business/billing/__init__.py', 'app/services/business/billing/payment_providers/__init__.py', 'app/services/services/paypal_service.py', 'app/api/routes/webhooks.py', 'app/api/v1/analytics.py', 'app/services/business/billing/receipt_generator.py', 'app/services/business/billing/services/payment_manager.py', 'app/services/business/billing/payment_providers/paypal.py', 'app/services/business/billing/schemas/base.py', 'app/services/schemas/__init__.py', 'app/services/models/subscription.py', 'app/services/models/payment.py', 'app/services/business/billing/usage_tracker.py', 'app/services/models/base.py', 'app/services/business/billing/services/paypal_service.py', 'app/services/schemas/payment.py', 'app/services/schemas/subscription.py', 'app/services/inter_service_client.py', 'app/services/usage_tracker.py', 'app/services/services/mpesa_service.py', 'app/services/services/base.py', 'app/services/models/enums.py', 'app/services/enhanced_plan_management_service.py', 'app/models/database.py', 'app/services/business/billing/services/payment.py', 'app/services/business/billing/exceptions.py', 'app/services/business/billing/models.py', 'app/api/__init__.py', 'app/services/business/billing/payment_providers/base.py', 'app/services/business/billing/services/mpesa_service.py', 'app/services/business/billing/services/base.py', 'app/services/services/stripe_service.py', 'app/services/exceptions.py', 'app/services/models.py', 'app/api/v1/plans_management.py', 'app/services/schemas/base.py', 'app/api/routes/payments/endpoints.py', 'app/api/routes/billing.py', 'app/services/business/billing/payment_providers/stripe.py', 'app/main.py', 'app/services/subscription_role_manager.py', 'app/services/business/billing/schemas/subscription.py'. Reloading...
WARNING:main:Service-specific routes not found
INFO:     Started server process [98354]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 08:52:17.924 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 08:52:17.925 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 08:52:18.179 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 08:52:18.181 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 08:52:18.247 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 08:52:18.250 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/credit_card.py', 'app/services/business/billing/payment_providers/stripe_service.py', 'app/services/payment_providers/stripe_service.py', 'app/payment_providers/mpesa.py', 'app/services/payment_providers/base.py', 'app/services/payment_providers/paypal.py', 'app/payment_providers/paypal.py', 'app/api/endpoints.py', 'app/services/business/billing/services/subscription.py', 'app/services/payment_providers/stripe.py', 'app/services/payment_providers/mpesa.py', 'app/payment_providers/stripe.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [98354]
WARNING:main:Service-specific routes not found
INFO:     Started server process [98667]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 08:53:03.292 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 08:53:03.293 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 08:53:03.515 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 08:53:03.516 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized successfully
2025-06-24 08:53:03.565 | ERROR    | app.services.inter_service_client:_register_billing_service:91 - ❌ Service registration failed: All connection attempts failed
2025-06-24 08:53:03.568 | INFO     | app.services.inter_service_client:initialize:39 - ✅ Billing service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/credit_card.py', 'app/services/business/billing/services/stripe_service.py', 'app/api/routes/subscriptions.py', 'app/services/subscription_manager.py', 'app/api/dependencies.py', 'app/services/business/billing/subscription_role_manager.py', 'main.py', 'app/services/services/payment_manager.py', 'app/api/endpoints.py', 'app/services/business/billing/subscription_manager.py', 'app/services/business/billing/payment_providers/mpesa.py', 'app/services/services/subscription.py', 'app/services/business/billing/schemas/payment.py', 'app/services/services/payment.py', 'app/webhooks/payment_webhooks.py', 'app/services/business/billing/__init__.py', 'app/services/business/billing/payment_providers/__init__.py', 'app/services/services/paypal_service.py', 'app/api/routes/webhooks.py', 'app/services/payment_providers/__init__.py', 'app/api/v1/analytics.py', 'app/services/business/billing/receipt_generator.py', 'app/services/business/billing/services/payment_manager.py', 'app/services/business/billing/payment_providers/paypal.py', 'app/services/business/billing/schemas/base.py', 'app/services/schemas/__init__.py', 'app/services/payment_providers/paypal.py', 'app/services/models/subscription.py', 'app/services/models/payment.py', 'app/services/business/billing/usage_tracker.py', 'app/services/business/billing/services/subscription.py', 'app/services/models/base.py', 'app/services/business/billing/services/paypal_service.py', 'app/services/schemas/payment.py', 'app/services/schemas/subscription.py', 'app/services/inter_service_client.py', 'app/services/usage_tracker.py', 'app/services/services/mpesa_service.py', 'app/services/services/base.py', 'app/services/models/enums.py', 'app/services/enhanced_plan_management_service.py', 'app/models/database.py', 'app/services/business/billing/services/payment.py', 'app/services/business/billing/exceptions.py', 'app/services/business/billing/models.py', 'app/services/payment_providers/credit_card.py', 'app/services/payment_providers/stripe_service.py', 'app/api/__init__.py', 'app/services/business/billing/payment_providers/base.py', 'app/services/business/billing/services/mpesa_service.py', 'app/services/business/billing/services/base.py', 'app/services/payment_providers/base.py', 'app/services/services/stripe_service.py', 'app/services/exceptions.py', 'app/services/business/billing/schemas/__init__.py', 'app/services/models.py', 'app/api/v1/plans_management.py', 'app/services/schemas/base.py', 'app/api/routes/payments/endpoints.py', 'app/api/routes/billing.py', 'app/services/business/billing/payment_providers/stripe.py', 'app/services/payment_providers/stripe.py', 'app/main.py', 'app/services/subscription_role_manager.py', 'app/services/payment_providers/mpesa.py', 'app/services/business/billing/schemas/subscription.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [98667]
Process SpawnProcess-9:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 17, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 15, in <module>
    from app.api.dependencies import get_current_user
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/dependencies.py", line 10, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
WARNING:  WatchFiles detected changes in '.venv/lib64/python3.11/site-packages/jwt/types.py', '.venv/lib64/python3.11/site-packages/jwt/exceptions.py', '.venv/lib64/python3.11/site-packages/jwt/api_jwt.py', '.venv/lib64/python3.11/site-packages/jwt/api_jws.py', '.venv/lib64/python3.11/site-packages/jwt/jwk_set_cache.py', '.venv/lib64/python3.11/site-packages/jwt/utils.py', '.venv/lib64/python3.11/site-packages/jwt/__init__.py', '.venv/lib64/python3.11/site-packages/jwt/warnings.py', '.venv/lib64/python3.11/site-packages/jwt/api_jwk.py', '.venv/lib64/python3.11/site-packages/jwt/help.py', '.venv/lib64/python3.11/site-packages/jwt/algorithms.py', '.venv/lib64/python3.11/site-packages/jwt/jwks_client.py'. Reloading...
=== 2025-06-24 09:09:38 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
ERROR:    [Errno 98] Address already in use
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [121204]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 09:09:45.647 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 09:09:45.648 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 09:09:45.896 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 09:09:45.897 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 09:09:46.092 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 09:09:46.094 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:54452 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [121204]
INFO:     Stopping reloader process [75015]
=== 2025-06-24 09:56:27 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [139658] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [139686]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 09:56:32.463 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 09:56:32.463 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 09:56:32.607 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 09:56:32.608 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 09:56:32.685 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 09:56:32.686 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
=== 2025-06-24 10:15:40 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [12246] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [12249]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 10:15:44.470 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 10:15:44.471 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 10:15:44.646 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 10:15:44.648 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:15:44.713 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:15:44.714 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:46260 - "GET / HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [12249]
WARNING:  WatchFiles detected changes in 'app/services/business/billing/services/subscription.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [13756]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 10:19:35.011 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 10:19:35.012 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 10:19:35.281 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 10:19:35.283 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:19:35.392 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:19:35.393 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [13756]
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/stripe_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/base.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/credit_card.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/mpesa.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/paypal.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/stripe.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/__init__.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/base.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/mpesa_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/payment.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/payment_manager.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/paypal_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/stripe_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/subscription.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/models.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/subscription_manager.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/subscription_role_manager.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/usage_tracker.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [14062]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 10:20:17.777 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 10:20:17.779 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 10:20:18.046 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 10:20:18.048 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 10:20:18.139 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 10:20:18.140 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14062]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [24376]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 11:00:08.721 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 11:00:08.722 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 11:00:08.970 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 11:00:08.971 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 11:00:09.034 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 11:00:09.035 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [24376]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [24820]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 11:02:17.971 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 11:02:17.973 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 11:02:18.258 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 11:02:18.260 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 11:02:18.349 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 11:02:18.351 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/business/billing/payment_providers/stripe_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [24820]
WARNING:  WatchFiles detected changes in 'app/services/business/billing/services/subscription.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [25601]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 11:05:43.188 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 11:05:43.190 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 11:05:43.477 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 11:05:43.480 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 11:05:43.611 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 11:05:43.613 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/models.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:main:🛑 Shutting down Billing Service...
INFO:main:✅ Billing Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [25601]
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/base.py', 'app/services/subscription_manager.py', 'app/services/payment_providers/credit_card.py', 'app/services/payment_providers/__init__.py', 'app/services/subscription_role_manager.py', 'app/services/usage_tracker.py', 'app/services/payment_providers/mpesa.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/payment_providers/paypal.py', 'app/services/payment_providers/stripe.py', 'app/services/services/base.py', 'app/services/payment_providers/stripe_service.py', 'app/services/services/mpesa_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/services/payment.py', 'app/services/services/paypal_service.py', 'app/services/services/stripe_service.py', 'app/services/services/payment_manager.py', 'app/services/services/subscription.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [25863]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 11:06:13.665 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 11:06:13.666 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 11:06:13.885 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 11:06:13.887 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 11:06:13.950 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 11:06:13.951 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:41684 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:41690 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41692 - "GET /docs HTTP/1.1" 200 OK
WARNING:fastapi:email-validator not installed, email fields will be treated as str.
To install, run: pip install email-validator
INFO:     127.0.0.1:41692 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:37722 - "GET /health HTTP/1.1" 200 OK
=== 2025-06-24 12:37:20 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [23088] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [23093]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Billing Service...
INFO:app.startup.vault_startup:🔐 Initializing Vault for billing
2025-06-24 12:37:29.292 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-24 12:37:29.293 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-24 12:37:29.627 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for billing
2025-06-24 12:37:29.629 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for billing, using environment variables
WARNING:app.startup.vault_startup:⚠️ Vault not available for billing, using environment variables
INFO:main:✅ Vault integration initialized
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 401 Unauthorized"
2025-06-24 12:37:29.702 | WARNING  | app.services.inter_service_client:_register_billing_service:94 - ⚠️ Failed to register with service registry: 401
2025-06-24 12:37:29.703 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:main:✅ Service registry registration completed
INFO:main:✅ Billing Service started successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:46186 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:53118 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:53120 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53118 - "GET /docs HTTP/1.1" 200 OK
WARNING:fastapi:email-validator not installed, email fields will be treated as str.
To install, run: pip install email-validator
INFO:     127.0.0.1:53118 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:53828 - "GET /api/v1/subscriptions/plans HTTP/1.1" 200 OK
INFO:     127.0.0.1:56940 - "GET /api/v1/subscriptions/plans/enterprise HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34388 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:34404 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38982 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48228 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:48228 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:56890 - "GET /api/v1/subscriptions/plans HTTP/1.1" 200 OK
INFO:     127.0.0.1:34342 - "GET /api/v1/subscriptions/plans HTTP/1.1" 200 OK
INFO:     127.0.0.1:57216 - "GET /api/v1/payments/providers HTTP/1.1" 200 OK
INFO:     127.0.0.1:47772 - "POST /api/v1/payments/ HTTP/1.1" 401 Unauthorized
