=== 2025-06-23 08:42:11 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
ERROR:    [Errno 98] Address already in use
=== 2025-06-23 12:22:04 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [13990] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [13995]
INFO:     Waiting for application startup.
2025-06-23 12:22:09.465 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 12:22:09.466 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 12:22:09.467 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 12:22:09.556 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 12:22:09.558 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 12:22:09.559 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 12:22:09.559 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 12:22:09.560 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 12:22:09.561 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 12:22:09.801 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 12:22:09.802 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 12:22:09.961 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 12:22:09.962 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 12:22:09.963 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 12:22:09.964 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 12:22:09.964 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 12:22:10.145 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 12:22:10.146 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 12:22:10.147 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 12:22:10.147 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 12:22:10.337 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 12:22:10.338 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 12:22:10.339 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 12:22:10.340 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 12:22:10.340 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 12:22:10.762 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 12:22:10.764 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 12:22:10.764 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 12:22:10.765 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 12:22:10.766 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 12:22:10.767 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 12:22:10.767 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:39608 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 15:37:04 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [14696] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [14700]
INFO:     Waiting for application startup.
2025-06-23 15:37:07.898 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 15:37:07.898 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 15:37:07.898 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 15:37:07.961 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 15:37:07.963 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 15:37:07.963 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 15:37:07.964 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 15:37:07.964 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 15:37:07.965 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 15:37:08.095 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 15:37:08.096 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 15:37:08.251 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 15:37:08.251 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 15:37:08.252 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 15:37:08.253 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 15:37:08.253 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 15:37:08.340 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 15:37:08.341 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 15:37:08.341 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 15:37:08.342 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 15:37:08.426 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 15:37:08.426 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 15:37:08.427 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 15:37:08.428 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 15:37:08.428 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 15:37:08.613 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 15:37:08.613 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 15:37:08.614 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 15:37:08.614 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 15:37:08.615 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 15:37:08.615 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 15:37:08.615 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:48134 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 18:21:29 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [14923] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [14939]
INFO:     Waiting for application startup.
2025-06-23 18:21:33.844 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 18:21:33.845 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 18:21:33.846 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 18:21:33.942 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 18:21:33.944 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 18:21:33.945 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 18:21:33.945 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 18:21:33.946 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 18:21:33.947 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 18:21:34.492 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 18:21:34.493 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 18:21:34.708 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 18:21:34.709 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 18:21:34.710 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 18:21:34.711 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 18:21:34.712 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 18:21:34.870 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 18:21:34.872 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 18:21:34.875 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 18:21:34.876 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 18:21:35.048 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 18:21:35.050 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 18:21:35.051 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 18:21:35.052 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 18:21:35.053 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 18:21:35.564 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 18:21:35.566 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 18:21:35.567 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 18:21:35.567 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 18:21:35.569 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 18:21:35.570 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 18:21:35.571 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:50848 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:01:39.805 | INFO     | main:shutdown_event:1559 - 🛑 Shutting down Monitoring Stack Service...
2025-06-23 20:01:39.812 | INFO     | main:shutdown_event:1560 - ✅ Monitoring Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14939]
INFO:     Stopping reloader process [14923]
=== 2025-06-23 20:04:40 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [72143] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [72149]
INFO:     Waiting for application startup.
2025-06-23 20:04:47.150 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 20:04:47.150 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 20:04:47.151 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 20:04:47.224 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 20:04:47.226 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 20:04:47.226 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 20:04:47.227 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 20:04:47.228 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 20:04:47.228 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 20:04:47.464 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 20:04:47.466 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 20:04:47.664 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 20:04:47.664 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 20:04:47.665 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 20:04:47.666 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 20:04:47.667 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:04:47.790 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 20:04:47.791 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 20:04:47.791 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 20:04:47.792 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:04:47.891 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 20:04:47.891 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 20:04:47.892 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 20:04:47.893 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 20:04:47.893 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:04:48.112 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 20:04:48.112 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 20:04:48.113 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 20:04:48.114 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 20:04:48.115 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 20:04:48.115 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 20:04:48.116 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:06:16.046 | INFO     | main:shutdown_event:1559 - 🛑 Shutting down Monitoring Stack Service...
2025-06-23 20:06:16.047 | INFO     | main:shutdown_event:1560 - ✅ Monitoring Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [72149]
INFO:     Stopping reloader process [72143]
=== 2025-06-23 20:09:17 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [79193] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [79198]
INFO:     Waiting for application startup.
2025-06-23 20:09:22.193 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 20:09:22.194 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 20:09:22.194 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 20:09:22.277 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 20:09:22.278 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 20:09:22.279 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 20:09:22.280 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 20:09:22.280 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 20:09:22.281 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 20:09:22.591 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 20:09:22.592 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 20:09:22.780 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 20:09:22.781 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 20:09:22.782 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 20:09:22.783 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 20:09:22.784 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:09:22.915 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 20:09:22.917 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 20:09:22.918 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 20:09:22.919 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:09:23.126 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 20:09:23.127 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 20:09:23.128 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 20:09:23.129 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 20:09:23.129 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:09:23.376 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 20:09:23.377 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 20:09:23.378 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 20:09:23.379 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 20:09:23.380 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 20:09:23.380 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 20:09:23.381 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-23 20:17:09.334 | INFO     | main:shutdown_event:1559 - 🛑 Shutting down Monitoring Stack Service...
2025-06-23 20:17:09.338 | INFO     | main:shutdown_event:1560 - ✅ Monitoring Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [79198]
INFO:     Stopping reloader process [79193]
=== 2025-06-23 20:21:48 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [88685] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [88688]
INFO:     Waiting for application startup.
2025-06-23 20:21:53.520 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 20:21:53.521 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 20:21:53.522 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 20:21:53.617 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 20:21:53.619 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 20:21:53.620 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 20:21:53.621 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 20:21:53.622 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 20:21:53.623 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 20:21:54.257 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 20:21:54.258 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 20:21:54.410 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 20:21:54.410 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 20:21:54.412 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 20:21:54.412 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 20:21:54.413 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:21:54.506 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 20:21:54.507 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 20:21:54.507 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 20:21:54.508 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:21:54.602 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 20:21:54.603 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 20:21:54.603 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 20:21:54.604 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 20:21:54.604 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 20:21:54.784 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 20:21:54.785 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 20:21:54.786 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 20:21:54.786 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 20:21:54.787 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 20:21:54.787 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 20:21:54.787 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:53930 - "GET / HTTP/1.1" 200 OK
=== 2025-06-23 21:02:40 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [11982] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [11986]
INFO:     Waiting for application startup.
2025-06-23 21:02:43.939 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-23 21:02:43.940 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-23 21:02:43.940 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-23 21:02:43.995 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-23 21:02:43.996 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-23 21:02:43.996 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-23 21:02:43.997 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-23 21:02:43.997 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-23 21:02:43.997 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-23 21:02:44.107 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-23 21:02:44.107 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-23 21:02:44.190 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-23 21:02:44.190 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-23 21:02:44.191 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-23 21:02:44.191 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-23 21:02:44.191 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 21:02:44.251 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-23 21:02:44.252 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-23 21:02:44.252 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-23 21:02:44.252 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 21:02:44.309 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-23 21:02:44.310 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-23 21:02:44.310 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-23 21:02:44.310 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-23 21:02:44.310 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-23 21:02:44.432 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-23 21:02:44.432 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-23 21:02:44.432 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-23 21:02:44.433 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-23 21:02:44.433 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-23 21:02:44.433 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-23 21:02:44.433 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:54714 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 06:27:48.656 | INFO     | main:shutdown_event:1559 - 🛑 Shutting down Monitoring Stack Service...
2025-06-24 06:27:48.675 | INFO     | main:shutdown_event:1560 - ✅ Monitoring Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [11986]
INFO:     Stopping reloader process [11982]
=== 2025-06-24 06:29:03 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [54083] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [54087]
INFO:     Waiting for application startup.
2025-06-24 06:29:11.283 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 06:29:11.285 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 06:29:11.287 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-24 06:29:11.422 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-24 06:29:11.424 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-24 06:29:11.425 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 06:29:11.425 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 06:29:11.426 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 06:29:11.426 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 06:29:11.828 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 06:29:11.829 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 06:29:12.044 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 06:29:12.044 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 06:29:12.046 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 06:29:12.046 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 06:29:12.047 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 06:29:12.214 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 06:29:12.215 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 06:29:12.217 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 06:29:12.219 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 06:29:12.406 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 06:29:12.407 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 06:29:12.408 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 06:29:12.408 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 06:29:12.409 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 06:29:12.755 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 06:29:12.755 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 06:29:12.756 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 06:29:12.756 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 06:29:12.757 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 06:29:12.757 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 06:29:12.758 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:42976 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 09:05:07 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
ERROR:    [Errno 98] Address already in use
INFO:     127.0.0.1:48046 - "GET / HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 09:14:40.681 | INFO     | main:shutdown_event:1559 - 🛑 Shutting down Monitoring Stack Service...
2025-06-24 09:14:40.778 | INFO     | main:shutdown_event:1560 - ✅ Monitoring Stack Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [54087]
INFO:     Stopping reloader process [54083]
=== 2025-06-24 09:16:33 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [124984] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [124997]
INFO:     Waiting for application startup.
2025-06-24 09:16:36.551 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 09:16:36.551 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 09:16:36.552 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-24 09:16:36.632 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-24 09:16:36.634 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-24 09:16:36.634 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 09:16:36.635 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 09:16:36.635 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 09:16:36.636 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 09:16:36.937 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 09:16:36.938 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 09:16:37.057 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 09:16:37.058 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 09:16:37.058 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 09:16:37.059 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 09:16:37.059 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 09:16:37.140 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 09:16:37.141 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 09:16:37.142 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 09:16:37.142 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 09:16:37.222 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 09:16:37.223 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 09:16:37.223 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 09:16:37.224 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 09:16:37.225 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 09:16:37.452 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 09:16:37.452 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 09:16:37.453 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 09:16:37.454 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 09:16:37.454 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 09:16:37.455 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 09:16:37.455 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 10:14:32 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [11837] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [11841]
INFO:     Waiting for application startup.
2025-06-24 10:14:36.680 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 10:14:36.680 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 10:14:36.681 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-24 10:14:36.776 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-24 10:14:36.778 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-24 10:14:36.779 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 10:14:36.779 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 10:14:36.780 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 10:14:36.781 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 10:14:36.972 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 10:14:36.973 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 10:14:37.122 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 10:14:37.122 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 10:14:37.123 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 10:14:37.124 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 10:14:37.125 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 10:14:37.219 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 10:14:37.220 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 10:14:37.221 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 10:14:37.222 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 10:14:37.344 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 10:14:37.345 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 10:14:37.345 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 10:14:37.346 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 10:14:37.346 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 10:14:37.589 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 10:14:37.590 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 10:14:37.590 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 10:14:37.591 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 10:14:37.591 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 10:14:37.592 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 10:14:37.592 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36932 - "GET / HTTP/1.1" 200 OK
=== 2025-06-24 12:36:03 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [22630] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [22633]
INFO:     Waiting for application startup.
2025-06-24 12:36:08.800 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 12:36:08.801 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 12:36:08.802 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
2025-06-24 12:36:08.898 | WARNING  | app.client.vault_http_client:initialize:50 - ⚠️ Vault-consul service not available for monitoring-stack
2025-06-24 12:36:08.900 | WARNING  | app.vault_integration:initialize:41 - ⚠️ Vault not available for monitoring-stack, using environment variables
2025-06-24 12:36:08.901 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 12:36:08.901 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 12:36:08.902 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 12:36:08.903 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 12:36:09.379 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 12:36:09.381 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 12:36:09.527 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 12:36:09.528 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 12:36:09.529 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 12:36:09.530 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 12:36:09.531 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 12:36:09.757 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 12:36:09.757 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 12:36:09.758 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 12:36:09.759 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 12:36:09.933 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 12:36:09.934 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 12:36:09.935 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 12:36:09.936 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 12:36:09.936 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 12:36:10.230 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 12:36:10.231 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 12:36:10.231 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 12:36:10.232 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 12:36:10.232 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 12:36:10.233 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 12:36:10.233 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:55956 - "GET / HTTP/1.1" 200 OK
